package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogImageUtils.buildMasterCatalogImage;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataDuplicationUtils.buildMasterCatalogRawDataDuplication;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogRawDataResourceApiUtils;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import com.mercaso.data.recommendation.dto.PageableResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

@Slf4j
class MasterCatalogRawDataResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogRawDataResourceApiUtils masterCatalogRawDataResourceApiUtils;

    @Autowired
    private MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogImageRepository masterCatalogImageRepository;

    @MockBean
    private ExternalApiAdapter externalApiAdapter;

    @MockBean
    private S3OperationAdapter s3OperationAdapter;

    @Test
    void searchMasterCatalogRawData() {

        MasterCatalogRawData masterCatalogRawData1 = buildMasterCatalogRawData(UUID.randomUUID());
        MasterCatalogRawData masterCatalogRawData2 = buildMasterCatalogRawData(UUID.randomUUID());
        MasterCatalogRawData masterCatalogRawData3 = buildMasterCatalogRawData(UUID.randomUUID());
        List<MasterCatalogRawData> saved = masterCatalogRawDataRepository.saveAll(List.of(masterCatalogRawData1,
            masterCatalogRawData2,
            masterCatalogRawData3));

        MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(saved.getFirst().getId());
        masterCatalogImageRepository.save(masterCatalogImage);

        CustomPage<MasterCatalogRawDataDto> result = masterCatalogRawDataResourceApiUtils.getMasterCatalogRawData();
        assert result.getData().size() >= 2;
        assert result.getTotalCount() >= 3;
    }

    @Test
    void searchMasterCatalogRawDataByImage() throws JsonProcessingException {
        String upc = String.valueOf(Instant.now().getEpochSecond());
        MasterCatalogRawData masterCatalogRawData = buildMasterCatalogRawData(UUID.randomUUID());
        masterCatalogRawData.setUpc(upc);
        MasterCatalogRawData saved = masterCatalogRawDataRepository.saveAndFlush(masterCatalogRawData);
        log.info("searchMasterCatalogRawDataByImage Saved: {}", saved);
        MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(saved.getId());
        MasterCatalogImage save = masterCatalogImageRepository.saveAndFlush(masterCatalogImage);
        log.info("searchMasterCatalogRawDataByImage Saved: {}", save);
        when(externalApiAdapter.getUpcsFromExternalApiByImage(any())).thenReturn(List.of(upc));
        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://test.com");

        CustomPage<MasterCatalogRawDataDto> result = masterCatalogRawDataResourceApiUtils.searchMasterCatalogRawDataByImage();
        log.info("searchMasterCatalogRawDataByImage Result: {}", result);
        assert result.getData().stream().anyMatch(x -> x.getUpc().equalsIgnoreCase(upc));
        assert result.getTotalCount() >= 1;
    }

    @Test
    void getMasterCatalogRawDataById() {
        MasterCatalogRawData masterCatalogRawData = buildMasterCatalogRawData(UUID.randomUUID());
        masterCatalogRawData.setUpc(String.valueOf(Instant.now().getEpochSecond()));
        MasterCatalogRawData saved = masterCatalogRawDataRepository.saveAndFlush(
            masterCatalogRawData);
        log.info("getMasterCatalogRawDataById Saved: {}", saved);
        MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(saved.getId());
        masterCatalogImageRepository.saveAndFlush(masterCatalogImage);

        MasterCatalogRawDataDto result = masterCatalogRawDataResourceApiUtils.getMasterCatalogRawDataById(
            saved.getId());
        assert result.getUpc().equalsIgnoreCase(saved.getUpc());
    }

    @Test
    void searchMasterCatalogDuplicationRawData() {
        UUID duplicationGroup = UUID.randomUUID();
        String upc1 = "847693759673294729475729574248";
        String upc2 = "767389462389346438492675967284";
        String upc3 = "867748294756867238473765682904";
        String upc4 = "563416380786824186036496759026";

        log.info("upcs : {}", List.of(upc1, upc2, upc3, upc4));

        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication1 = buildMasterCatalogRawDataDuplication(upc1, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication2 = buildMasterCatalogRawDataDuplication(upc2, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication3 = buildMasterCatalogRawDataDuplication(upc3, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication4 = buildMasterCatalogRawDataDuplication(upc4, duplicationGroup);

        MasterCatalogRawData masterCatalogRawData1 = buildMasterCatalogRawData(upc2);
        MasterCatalogRawData masterCatalogRawData2 = buildMasterCatalogRawData(upc3);
        MasterCatalogRawData masterCatalogRawData3 = buildMasterCatalogRawData(upc4);
        masterCatalogRawDataDuplicationRepository.saveAll(List.of(masterCatalogRawDataDuplication1,
                masterCatalogRawDataDuplication2,
                masterCatalogRawDataDuplication3,
                masterCatalogRawDataDuplication4));
        List<MasterCatalogRawData> masterCatalogRawDataList = masterCatalogRawDataRepository.saveAll(List.of(masterCatalogRawData1,
                masterCatalogRawData2,
                masterCatalogRawData3));

        List<UUID> ids = masterCatalogRawDataList.stream().map(MasterCatalogRawData::getId).toList();
        ids.forEach(id -> {
            MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(id);
            masterCatalogImageRepository.saveAndFlush(masterCatalogImage);
        });

        CustomPage<MasterCatalogRawDataDto> rawData = masterCatalogRawDataResourceApiUtils.getMasterCatalogDuplicationRawData(upc1);

        List<String> upcList = rawData.getData().stream().map(MasterCatalogRawDataDto::getUpc).toList();

        log.info("getMasterCatalogDuplicationRawData Result: {}", rawData);

        assert upcList.containsAll(List.of(upc2,upc3,upc4));
    }
}
