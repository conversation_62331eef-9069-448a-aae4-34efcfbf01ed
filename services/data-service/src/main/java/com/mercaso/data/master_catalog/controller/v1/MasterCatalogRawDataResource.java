package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/master-catalog/v1/raw-data")
public class MasterCatalogRawDataResource {

  private final MasterCatalogRawDataService masterCatalogRawDataService;

  @PreAuthorize("hasAnyAuthority('master-catalog:read:raw-data')")
  @GetMapping("/search")
  public ResponseEntity<CustomPage<MasterCatalogRawDataDto>> searchMasterCatalogRawData(
      @ModelAttribute @Valid SearchMasterCatalogRequest searchRequest) {

    return ResponseEntity.ok(
        new CustomPage<MasterCatalogRawDataDto>().build(masterCatalogRawDataService.searchMasterCatalogRawData(
            searchRequest)));
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:read:raw-data')")
  @PostMapping("/search-by-image")
  public ResponseEntity<CustomPage<MasterCatalogRawDataDto>> searchMasterCatalogRawDataByImage(
      @Valid @NotNull @RequestParam("file") MultipartFile file,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "20") Integer pageSize) {

    Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawDataByImage(file,
        PageRequest.of(page - 1, pageSize));

    return ResponseEntity.ok(new CustomPage<MasterCatalogRawDataDto>().build(result));
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:read:raw-data')")
  @GetMapping("/{id}")
  public ResponseEntity<MasterCatalogRawDataDto> findById(@PathVariable("id") UUID id) {
    return ResponseEntity.ok(masterCatalogRawDataService.findById(id));
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:read:raw-data')")
  @GetMapping("/duplication/search")
  public CustomPage<MasterCatalogRawDataDto> searchMasterCatalogDuplicationRawData(
      @RequestParam(name = "page", defaultValue = "0") Integer page,
      @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize,
      @RequestParam(name = "upc") String upc) {
    return new CustomPage<MasterCatalogRawDataDto>().build(
        masterCatalogRawDataService.searchDuplicationRawData(page, pageSize, upc));
  }
}
