package com.mercaso.ims.application.service.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BrandApplicationServiceImplTest {

    @Mock
    BrandService brandService;
    @Mock
    BrandDtoApplicationMapper brandDtoApplicationMapper;
    @InjectMocks
    BrandApplicationServiceImpl brandApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateBrand() {

        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        UUID id = UUID.randomUUID();

        when(brandService.save(any(Brand.class))).thenReturn(Brand.builder().id(id).name(brandName).build());
        when(brandDtoApplicationMapper.domainToDto(any(Brand.class))).thenReturn(new BrandDto(id, brandName));

        BrandDto result = brandApplicationServiceImpl.createBrand(new CreateBrandCommand(brandName, "logo", "description"));
        Assertions.assertEquals(id, result.getBrandId());
        Assertions.assertEquals(brandName, result.getBrandName());
    }
}