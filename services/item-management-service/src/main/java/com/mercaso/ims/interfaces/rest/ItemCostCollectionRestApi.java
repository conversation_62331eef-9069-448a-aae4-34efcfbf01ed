package com.mercaso.ims.interfaces.rest;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import java.io.IOException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping(value = "/v1/item-cost-collection", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemCostCollectionRestApi {

    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;


    @GetMapping("/{id}/file")
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public DocumentResponse getItemCostCollectionFile(@PathVariable UUID id) {
        log.info("[getItemCostCollectionFile] param itemCostCollectionId: {}.", id);
        return itemCostCollectionApplicationService.getItemCostCollectionFile(id);
    }

    @PostMapping("/jetro-cost-collection")
    @PreAuthorize("hasAuthority('ims:read:vendor-items')")
    public void createJetroCostCollection(@RequestParam("file") MultipartFile file) throws IOException {
        log.info("[createJetroCostCollection] fileName: {}.", file.getOriginalFilename());
        byte[] bytes = file.getBytes();
        itemCostCollectionApplicationService.createJetroCostCollection(bytes);
    }
    
}