package com.mercaso.ims.application.service.impl;

import static com.mercaso.document.operations.constants.CommonSymbols.SLASH;
import static com.mercaso.ims.application.query.ItemQuery.getCustomFilterValue;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.FAILED_TO_GENERATE_THE_MERCHANDISE_REPORT;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.GET_CHANGE_RESULT_MERCHANDISE_REPORT_FILE_ERROR;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.ItemQuantityDto;
import com.mercaso.data.client.dto.WeeklyItemSalesDto;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.MerchandiseReportService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.excel.data.MerchandiseReportData;
import com.mercaso.ims.infrastructure.excel.generator.MerchandiseReportExcelGenerator;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import com.mercaso.ims.infrastructure.external.google.GoogleDriverAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MerchandiseReportServiceImpl implements MerchandiseReportService {

    private static final String VERSION_NUMBER = "Version";
    private final MerchandiseReportExcelGenerator excelGenerator;
    private final ItemQueryApplicationService itemQueryService;
    private final GoogleDriverAdaptor googleDriverAdaptor;
    private final ShopifyAdaptor shopifyAdaptor;
    private final FinaleAdaptor finaleAdaptor;
    private final BrandService brandService;
    private final VendorService vendorService;
    private final DocumentApplicationService documentApplicationService;
    private final CategoryApplicationService categoryApplicationService;
    private final ItemSearchApplicationService itemSearchApplicationService;
    private final BusinessEventService businessEventService;
    private final BulkExportRecordsApplicationService bulkExportRecordsApplicationService;
    private final Executor taskExecutor;
    private static final String EXTENSION = ".xlsx";

    @Override
    @Async("taskExecutor")
    @ReportMetric(metricsType = MetricsTypeEnum.GENERATE_MERCHANDISE_REPORT_FILE)
    public void getMerchandiseReportFile() {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.of("America/Los_Angeles"));
        String date = localDateTime.format(DateTimeFormatter.ofPattern("MMddyyyy-HH"));
        String baseName = "Mercaso_Merchandise_Report";
        String fileName = getFileName(date, baseName);
        String encodedFileName = URLEncoder.encode(fileName + EXTENSION, StandardCharsets.UTF_8);

        generateMerchandiseReportFile(encodedFileName,
            null,
            null,
            true, null);
    }

    @Override
    public void getChangeResultMerchandiseReportFile(HttpServletResponse httpResponse,
        Instant updatedAtStartTime,
        Instant updatedAtEndTime) {
        try {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
            Instant yesterdayEndInstant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            String date = yesterdayEndInstant.atZone(ZoneId.systemDefault())
                .toLocalDateTime()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String baseName = "Change_Result_Merchandise_Report";
            String fileName = getFileName(date, baseName);
            String encodedFileName = URLEncoder.encode(fileName + EXTENSION, StandardCharsets.UTF_8);

            byte[] excelData = generateMerchandiseReportFile(encodedFileName,
                updatedAtStartTime,
                updatedAtEndTime,
                false, null);
            Tika tika = new Tika();
            String excelType = tika.detect(excelData);
            httpResponse.setContentType(excelType);
            httpResponse.setCharacterEncoding("utf-8");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
            log.info("[getChangeResultMerchandiseReportFile] downloaded successfully: {}, excelData.length: {}",
                encodedFileName, excelData.length);
            httpResponse.getOutputStream().write(excelData);
        } catch (IOException e) {
            log.error("[getChangeResultMerchandiseReportFile] error : ", e);
            throw new ImsBusinessException(GET_CHANGE_RESULT_MERCHANDISE_REPORT_FILE_ERROR);
        }
    }

    @Override
    @Async("taskExecutor")
    public void downloadFilteredMerchandiseReport(String customFilter) {
        Instant now = Instant.now();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(now, ZoneId.systemDefault());
        String date = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String baseName = "Custom_Filter_Merchandise_Report";
        String fileName = getFileName(date, baseName);
        String encodedFileName = URLEncoder.encode(fileName + EXTENSION, StandardCharsets.UTF_8);

        generateMerchandiseReportFile(encodedFileName,
            null,
            null,
            false, customFilter);

        BulkExportRecordsCommand command = BulkExportRecordsCommand.builder()
            .fileName("documents" + SLASH + encodedFileName)
            .searchTime(now)
            .customFilter(customFilter)
            .build();
        BulkExportRecordsDto bulkExportRecordsDto = bulkExportRecordsApplicationService.save(command);

        businessEventService.dispatch(BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsDto.getId())
            .data(bulkExportRecordsDto)
            .build());
    }


    private byte[] generateMerchandiseReportFile(String encodedFileName, Instant updatedAtStartTime,
        Instant updatedAtEndTime,  boolean uploadToGoogleDrive, String customFilter) {
        try {
            log.info("[getMerchandiseReportFile] Generating merchandise report");
            long fetchAllItemsConcurrentlyBegin = System.currentTimeMillis();

            List<FinaleAvailableStockDto> finaleData = finaleAdaptor.getAllProducts();
            WeeklyItemSalesDto weeklyItemSalesDto = shopifyAdaptor.getWeeklySkuSalesData();
            Map<String, Long> sales4WOSMap = Optional.ofNullable(weeklyItemSalesDto.getFourWeeklyData())
                .orElse(Collections.emptyList())
                .stream()
                .filter(data -> data.getItem() != null && data.getQuantity() != null)
                .collect(Collectors.toMap(ItemQuantityDto::getItem, ItemQuantityDto::getQuantity, (a, b) -> a));

            Map<String, Long> sales1WOSMap = Optional.ofNullable(weeklyItemSalesDto.getWeeklyData())
                .orElse(Collections.emptyList())
                .stream()
                .filter(data -> data.getItem() != null && data.getQuantity() != null)
                .collect(Collectors.toMap(ItemQuantityDto::getItem, ItemQuantityDto::getQuantity, (a, b) -> a));

            Map<UUID, Brand> brandMap = brandService.findAll().stream()
                .collect(Collectors.toMap(Brand::getId, b -> b));
            Map<UUID, Vendor> vendorMap = vendorService.findAll().stream()
                .collect(Collectors.toMap(Vendor::getId, v -> v));
            Map<UUID, Map<Integer, String>> allLeafNodesWithAncestors = categoryApplicationService.getAllLeafNodesWithAncestors();

            List<MerchandiseReportData> allItems = fetchAllItemsConcurrently(finaleData,
                sales4WOSMap,
                sales1WOSMap,
                brandMap,
                vendorMap, updatedAtStartTime, updatedAtEndTime, allLeafNodesWithAncestors, customFilter);
            long fetchAllItemsConcurrentlyEnd = System.currentTimeMillis();
            log.info("[getMerchandiseReportFile] Fetching all items concurrently took {} ms",
                fetchAllItemsConcurrentlyEnd - fetchAllItemsConcurrentlyBegin);

            long generateReportBegin = System.currentTimeMillis();
            byte[] excelContent = excelGenerator.generateReport(allItems);
            log.info("[getMerchandiseReportFile] Generating merchandise report excelContent: {} ", excelContent.length);
            long generateReportEnd = System.currentTimeMillis();
            log.info("[getMerchandiseReportFile] Generating merchandise report took {} ms",
                generateReportEnd - generateReportBegin);

            // Upload to the document service instead of local saving
            log.info("Uploading merchandise report with file name: {}", encodedFileName);
            DocumentResponse documentResponse = documentApplicationService.uploadExcel(excelContent, encodedFileName);
            log.info("Document uploaded with name: {}, url :{}", documentResponse.getName(), documentResponse.getSignedUrl());
            if (uploadToGoogleDrive) {
                googleDriverAdaptor.uploadFileToGoogleDriver(encodedFileName, excelContent);
            }
            return excelContent;
        } catch (Exception e) {
            log.error("Error generating merchandise report", e);
            throw new ImsBusinessException(FAILED_TO_GENERATE_THE_MERCHANDISE_REPORT);
        }
    }

    public List<MerchandiseReportData> fetchAllItemsConcurrently(List<FinaleAvailableStockDto> finaleData,
        Map<String, Long> sales4WOSMap,
        Map<String, Long> sales1WOSMap,
        Map<UUID, Brand> brandMap,
        Map<UUID, Vendor> vendorMap,
        Instant updatedAtStartTime, Instant updatedAtEndTime,
        Map<UUID, Map<Integer, String>> allLeafNodesWithAncestors,
        String customFilter) {
        int pageSize = 2000;
        AtomicInteger pageNumber = new AtomicInteger(0);

        AtomicBoolean isLastPage = new AtomicBoolean(false);

        return StreamSupport.stream(
                Spliterators.spliteratorUnknownSize(new Iterator<List<MerchandiseReportData>>() {
                    @Override
                    public boolean hasNext() {
                        return !isLastPage.get();
                    }

                    @Override
                    public List<MerchandiseReportData> next() {
                        if (!hasNext()) {
                            throw new NoSuchElementException();
                        }
                        PageRequest pageRequest = PageRequest.of(pageNumber.get(),
                            pageSize,
                            Sort.by(Order.asc("skuNumber")));

                        Page<ItemDto> page = getItemsByPage(pageRequest, updatedAtStartTime, updatedAtEndTime, customFilter);
                        List<ItemDto> itemDtos = page.getContent();
                        if (!page.hasNext()) {
                            isLastPage.set(true);
                        }
                        pageNumber.incrementAndGet();
                        return excelGenerator.mapItemsToReportData(itemDtos,
                            finaleData,
                            sales4WOSMap,
                            sales1WOSMap,
                            brandMap,
                            vendorMap,
                            allLeafNodesWithAncestors);
                    }
                }, Spliterator.ORDERED), true)
            .flatMap(List::stream)
            .toList();
    }

    private Page<ItemDto> getItemsByPage(PageRequest pageRequest, Instant updatedAtStartTime, Instant updatedAtEndTime, String customFilter) {
        log.info("[getItemsByPage] pageRequest:{}, customFilter: {}", pageRequest, customFilter);
        Map<String, String> customFilterValue = getCustomFilterValue(customFilter);
        if (StringUtils.isNotBlank(customFilter) && !customFilterValue.isEmpty()) {
            long begin = System.currentTimeMillis();
            ItemQuery itemQuery = ItemQuery.builder()
                .page(pageRequest.getPageNumber())
                .pageSize(pageRequest.getPageSize())
                .customFilter(customFilterValue)
                .build();
            List<UUID> itemIds = itemSearchApplicationService.searchItemListIds(itemQuery);
            log.info("[getItemsByPage]searchItemListIds end: {}", System.currentTimeMillis() - begin);
            Long totalCount = itemSearchApplicationService.searchItemCount(itemQuery);

            if (itemIds.isEmpty()) {
                return Page.empty(pageRequest);
            }

            int subPageSize = 200;
            int totalSize = itemIds.size();

            int batches = (int) Math.ceil((double) totalSize / subPageSize);

            List<CompletableFuture<List<ItemDto>>> futures = IntStream.range(0, batches)
                .mapToObj(i -> {
                    int start = i * subPageSize;
                    int end = Math.min(start + subPageSize, totalSize);
                    List<UUID> subPageItemIds = itemIds.subList(start, end);

                    return CompletableFuture.supplyAsync(() -> {
                        long parallelFindByIdInBegin = System.currentTimeMillis();
                        List<ItemDto> byIdIn = itemQueryService.findByIdIn(subPageItemIds);
                        log.info("[getItemsByPage]findByIdIn subPage: {}, size: {}, time: {}ms",
                                i, subPageItemIds.size(), System.currentTimeMillis() - parallelFindByIdInBegin);
                        return byIdIn;
                    }, taskExecutor);

                }).toList();

            List<ItemDto> allItemDtos = futures.stream()
                    .map(future -> {
                        try {
                            return future.join();
                        } catch (Exception e) {
                            log.error("Error retrieving batch in getItemsByPage", e);
                            return Collections.<ItemDto>emptyList();
                        }
                    })
                    .flatMap(List::stream)
                    .toList();

            return new PageImpl<>(allItemDtos, pageRequest, totalCount);
        }

        if (updatedAtStartTime != null && updatedAtEndTime != null) {
            return itemQueryService.findPagedItemsByUpdateAt(pageRequest, updatedAtStartTime, updatedAtEndTime);
        }

        return itemQueryService.findPagedItems(pageRequest);
    }

    private String getFileName(String date, String baseName) {
        return String.format("%s_%s_%s", VERSION_NUMBER, date, baseName);
    }
}

