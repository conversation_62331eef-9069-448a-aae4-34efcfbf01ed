package com.mercaso.ims.infrastructure.external.shopify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class ShopifyDeleteMateFieldsGraphQLDto {

    private String query = "mutation MetafieldsDelete($metafields: [MetafieldIdentifierInput!]!) { metafieldsDelete(metafields: $metafields) { deletedMetafields { key namespace ownerId } userErrors { field message } } }";

    private MetaFieldsDto variables;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetaFieldsDto {
        private List<MetaFieldDto> metafields;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetaFieldDto {
        private String key;
        private String namespace;
        private String ownerId;

        public void setOwnerId(String ownerId) {
            this.ownerId = "gid://shopify/Product/"+ownerId;
        }
    }



}
