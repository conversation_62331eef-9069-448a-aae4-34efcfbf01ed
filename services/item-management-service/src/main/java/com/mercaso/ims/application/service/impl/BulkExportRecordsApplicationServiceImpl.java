package com.mercaso.ims.application.service.impl;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.mapper.bulkexportrecords.BulkExportRecordsDtoApplicationMapper;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.BULK_EXPORT_RECORDS_NOT_FOUND;

@Service
@Slf4j
@RequiredArgsConstructor
public class BulkExportRecordsApplicationServiceImpl implements BulkExportRecordsApplicationService {

    private final BulkExportRecordsService bulkExportRecordsService;
    private final BulkExportRecordsDtoApplicationMapper bulkExportRecordsDtoApplicationMapper;
    private final DocumentApplicationService documentApplicationService;

    @Override
    @Transactional
    public BulkExportRecordsDto save(BulkExportRecordsCommand bulkExportRecordsCommand) {
        BulkExportRecords bulkExportRecords = BulkExportRecords.builder().fileName(bulkExportRecordsCommand.getFileName())
            .customFilter(bulkExportRecordsCommand.getCustomFilter())
            .searchTime(bulkExportRecordsCommand.getSearchTime())
            .sendEmailTime(bulkExportRecordsCommand.getSendEmailTime())
            .build();
        BulkExportRecords save = bulkExportRecordsService.save(bulkExportRecords);
        return bulkExportRecordsDtoApplicationMapper.domainToDto(save);
    }

    @Override
    public DocumentResponse getBulkExportRecordsFile(UUID id) {
        BulkExportRecords bulkExportRecords = bulkExportRecordsService.findById(id);
        if (bulkExportRecords == null) {
            throw new ImsBusinessException(BULK_EXPORT_RECORDS_NOT_FOUND);
        }
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName(bulkExportRecords.getFileName());
        String url = documentApplicationService.getSignedUrl(bulkExportRecords.getFileName());
        documentResponse.setSignedUrl(url);
        return documentResponse;
    }
}
