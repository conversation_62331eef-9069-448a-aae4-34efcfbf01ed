package com.mercaso.ims.infrastructure.external.aws_ocr;

import com.mercaso.ims.infrastructure.external.aws_ocr.dto.AnalyzeExpenseResponseDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.AnalyzeLineItemDto;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseFieldDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.LabelDetectionDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.LineItemDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.SummaryFieldDto;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ValueDetectionDTO;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisResponse;

@Slf4j
public class AnalyzeLineItemDtoFactory {

    private AnalyzeLineItemDtoFactory() {
        throw new UnsupportedOperationException("Utility class");
    }

    private static final String LABEL = "Item #";

    public static List<AnalyzeLineItemDto> mapToLineItemDtos(AnalyzeExpenseResponseDTO response) {
        return response.getExpenseDocuments().stream()
            .flatMap(doc -> doc.getLineItemGroups().stream())
            .flatMap(group -> group.getLineItems().stream())
            .map(AnalyzeLineItemDtoFactory::buildLineItemDto)
            .toList();
    }

    public static SummaryFieldDto mapToSummaryFieldDtos(AnalyzeExpenseResponseDTO response) {
        SummaryFieldDto dto = new SummaryFieldDto();
        response.getExpenseDocuments().forEach(doc -> {
            doc.getSummaryFields().forEach(field -> {
                switch (field.getType().getText()) {
                    case "INVOICE_RECEIPT_DATE" -> dto.setRcptDate(getFieldValue(field));
                    case "INVOICE_RECEIPT_ID" -> dto.setRcptId(getFieldValue(field));
                    default -> log.info("Unknown field type: {}", field.getType().getText());
                }
            });
        });

        return dto;
    }

    public static AnalyzeExpenseResponseDTO convert(GetExpenseAnalysisResponse source) {
        return ExpenseConverter.convert(source);
    }

    private static AnalyzeLineItemDto buildLineItemDto(LineItemDTO lineItemDTO) {
        AnalyzeLineItemDto dto = new AnalyzeLineItemDto();
        lineItemDTO.getLineItemExpenseFields().forEach(field -> {
            switch (field.getType().getText()) {
                case "PRODUCT_CODE" -> {
                    String label = getFieldLabel(field);
                    String value = getFieldValue(field);
                    if (null != label && label.contains(LABEL)) {
                        dto.setVendorSkuNumber(value);
                    } else {
                        dto.setUpcNumber(value);
                    }
                }
                case "ITEM" -> dto.setNewDescription(getFieldValue(field));
                case "PRICE" -> dto.setTotalCost(getFieldValue(field));
                case "UNIT_PRICE" -> dto.setCost(getFieldValue(field));
                case "QUANTITY" -> dto.setQuantity(getFieldValue(field));
                default -> log.info("Unknown LineItem field type: {}", field.getType().getText());
            }
        });

        return dto;
    }

    private static String getFieldValue(ExpenseFieldDTO fieldDTO) {
        return Optional.ofNullable(fieldDTO.getValueDetection())
            .map(ValueDetectionDTO::getText)
            .orElse(null);
    }

    private static String getFieldLabel(ExpenseFieldDTO fieldDTO) {
        return Optional.ofNullable(fieldDTO.getLabelDetection())
            .map(LabelDetectionDTO::getText)
            .orElse(null);
    }

}
