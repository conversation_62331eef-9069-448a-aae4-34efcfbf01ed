package com.mercaso.ims.infrastructure.external.shopify;

import com.google.api.client.util.Lists;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mercaso.data.client.api.SaleStatisticsControllerApi;
import com.mercaso.data.client.dto.WeeklyItemSalesDto;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.exception.ServiceRetryableException;
import com.mercaso.ims.infrastructure.external.shopify.dto.*;
import com.mercaso.ims.infrastructure.external.shopify.dto.ProductPublicationResponseDto.ProductData.Product;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Image;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Variant;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyQueryChannelResponseDto.Edge;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

@Slf4j
@Component
@RequiredArgsConstructor
public class ShopifyAdaptor {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final String SHOPIFY_HEADER = "X-Shopify-Access-Token";
    private static final String SHOP_API_CALL_LIMIT = "X-Shopify-Shop-Api-Call-Limit";
    private static final String COUNT = "count";
    private static final String SALE_CHANNEL_IDS = "SALE_CHANNEL_IDS";

    private final SaleStatisticsControllerApi saleStatisticsControllerApi;
    private final HttpClient client;
    private final ShopifyAccessTokenBalancer shopifyAccessTokenBalancer;
    private final RateLimiterRegistry rateLimiterRegistry;
    @Value("${shopify.host}")
    private String shopifyHost;
    @Value("${shopify.query_product_url}")
    private String shopifyQueryProductUrl;
    @Value("${shopify.create_product_url}")
    private String shopifyCreateProductUrl;
    @Value("${shopify.modify_product_url}")
    private String shopifyModifyProductUrl;
    @Value("${shopify.delete_product_url}")
    private String shopifyDeleteProductUrl;
    @Value("${shopify.modify_product_image_url}")
    private String modifyProductImageUrl;
    @Value("${shopify.remove_product_image_url}")
    private String removeProductImageUrl;
    @Value("${shopify.create_product_image_attach_to_product_variants_url}")
    private String createProductImageAttachToProductVariantsUrl;
    @Value("${shopify.create_product_meta_field_url}")
    private String createProductMetaFieldUrl;
    @Value("${shopify.modify_product_inventory_item_url}")
    private String modifyProductInventoryItemUrl;
    @Value("${shopify.set_product_channel_url}")
    private String setProductChannelUrl;
    @Value("${shopify.delete_product_meta_field_url}")
    private String deleteProductMetaFieldUrl;
    private static final Cache<String, List<String>> channelIdCache = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(7, TimeUnit.DAYS)
        .build();

    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public WeeklyItemSalesDto getWeeklySkuSalesData() {
        try {
            ResponseEntity<WeeklyItemSalesDto> responseEntity = saleStatisticsControllerApi.getWeeklySkuSalesDataWithHttpInfo();
            log.info("getWeeklySkuSalesData responseEntity:{}", responseEntity);
            return responseEntity.getBody();
        } catch (Exception e) {
            log.error("Get weekly sku sales data failed", e);
            handleRetryableException(e, "Exception calling get weekly sku sales data %s");
            throw e;
        }
    }

    @ReportMetric(metricsType = MetricsTypeEnum.QUERY_SHOPIFY_PRODUCT)
    public ShopifyGraphQLQueryResponseDto queryProduct(String sku) {
        ShopifyProductGraphQLDto build = ShopifyProductGraphQLDto.builder().sku(sku).build();
        String requestBody = build.buildQueryBySkuQraphQL();

        log.info("[queryProduct] request body: {}", requestBody);

        Request request = buildPostRequest(shopifyHost + shopifyQueryProductUrl, requestBody);

        return executeRequest(request, ShopifyGraphQLQueryResponseDto.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.CREATE_SHOPIFY_PRODUCT)
    public ShopifyProductDto createShopifyProduct(ShopifyProductDto shopifyProductDto) {

        log.info("[createShopifyProduct] request body: {}", shopifyProductDto);

        Request request = buildPostRequest(shopifyHost + shopifyCreateProductUrl, shopifyProductDto);
        ShopifyProductDto createdProduct = executeRequest(request, ShopifyProductDto.class);
        variantBindingImage(createdProduct);
        return createdProduct;
    }

    @ReportMetric(metricsType = MetricsTypeEnum.MODIFY_SHOPIFY_PRODUCT)
    public ShopifyProductDto modifyShopifyProduct(ShopifyProductDto shopifyProductDto) {

        log.info("[modifyShopifyProduct] request body: {}", shopifyProductDto);

        if (null == shopifyProductDto.getProduct() || null == shopifyProductDto.getProduct().getId()) {
            log.warn("[modifyShopifyProduct] productId is null");
            return null;
        }

        String url = buildShopifyUrlWithProductId(shopifyModifyProductUrl, shopifyProductDto.getProduct().getId());
        Request request = buildPutRequest(url, shopifyProductDto);
        return executeRequest(request, ShopifyProductDto.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.MODIFY_SHOPIFY_PRODUCT_IMAGE)
    public VariantImageDto modifyShopifyProductImage(VariantImageDto variantImageDto) {

        log.info("[modifyShopifyProductImage] request body: {}", variantImageDto);

        if (null == variantImageDto) {
            log.error("[modifyShopifyProductImage] variantImageDto is null");
            return null;
        }

        Image image = variantImageDto.getImage();
        Long imageId = image.getId();
        Long productId = image.getProductId();

        if (null == productId) {
            log.warn("[modifyShopifyProductImage] productId is null, variantImageDto:{}", variantImageDto);
            return null;
        }

        if (null == imageId && StringUtils.isBlank(image.getSrc())) {
            log.warn("[modifyShopifyProductImage] imageId and src is null, variantImageDto:{}", variantImageDto);
            return null;
        }

        if (null != imageId) {
            deleteShopifyProductImage(productId, imageId);
            if (StringUtils.isBlank(image.getSrc())) {
                log.warn("[modifyShopifyProductImage] src is null, variantImageDto:{}", variantImageDto);
                return null;
            }
        }

        String createNewImageUrl = buildShopifyUrlWithProductId(createProductImageAttachToProductVariantsUrl, productId);
        Request createNewImageRequest = buildPostRequest(createNewImageUrl, variantImageDto);
        return executeRequest(createNewImageRequest, VariantImageDto.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.DELETE_SHOPIFY_PRODUCT)
    public void deleteShopifyProduct(Long productId) {
        log.info("[deleteShopifyProduct] productId: {}", productId);

        if (null == productId) {
            log.warn("[deleteShopifyProduct] productId is null");
            return;
        }

        String url = buildShopifyUrlWithProductId(shopifyDeleteProductUrl, productId);

        executeRequest(buildDeleteRequest(url), Void.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.MODIFY_SHOPIFY_PRODUCT_META_FIELDS)
    public List<MetafieldDto> createOrModifyShopifyProductMetaFields(List<MetafieldDto> metafieldDtos,
        ShopifyProductDto shopifyProduct) {
        log.info("[createShopifyProductMetaFields] metafieldDtos: {}", metafieldDtos);
        List<MetafieldDto> metafieldDtoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(metafieldDtos) || null == shopifyProduct || null == shopifyProduct.getProduct()) {
            log.warn("[createShopifyProductMetaFields] metafieldDtos or shopifyProduct is null");
            return List.of();
        }
        metafieldDtos.forEach(metafieldDto -> {
            String url = buildShopifyUrlWithProductId(createProductMetaFieldUrl, shopifyProduct.getProduct().getId());
            Request request = buildPostRequest(url, metafieldDto);
            metafieldDtoList.add(executeRequest(request, MetafieldDto.class));
        });

        return metafieldDtoList;
    }

    @ReportMetric(metricsType = MetricsTypeEnum.MODIFY_SHOPIFY_PRODUCT_META_FIELDS)
    public ShopifyDeleteMetaFieldsResponseDto deletedShopifyProductMetaFields(List<MetafieldDto> metafieldDtos,
                                                                             ShopifyProductDto shopifyProduct) {
        log.info("[deletedShopifyProductMetaFields] metafieldDtos: {}", metafieldDtos);
        if (CollectionUtils.isEmpty(metafieldDtos) || shopifyProduct == null || shopifyProduct.getProduct() == null) {
            log.warn("[deletedShopifyProductMetaFields] metafieldDtos or shopifyProduct is null");
            return null;
        }

        Long productId = shopifyProduct.getProduct().getId();
        if (productId == null) {
            log.warn("[deletedShopifyProductMetaFields] productId is null");
            return null;
        }

        List<ShopifyDeleteMateFieldsGraphQLDto.MetaFieldDto> metaFields = metafieldDtos.stream()
            .map(dto -> {
                var metafield = new ShopifyDeleteMateFieldsGraphQLDto.MetaFieldDto();
                metafield.setOwnerId(productId.toString());
                metafield.setKey(dto.getMetafield().getKey());
                metafield.setNamespace(dto.getMetafield().getNamespace());
                return metafield;
            })
            .toList();

        ShopifyDeleteMateFieldsGraphQLDto.MetaFieldsDto metaFieldsDto = new ShopifyDeleteMateFieldsGraphQLDto.MetaFieldsDto();
        metaFieldsDto.setMetafields(metaFields);

        ShopifyDeleteMateFieldsGraphQLDto build = ShopifyDeleteMateFieldsGraphQLDto.builder().variables(metaFieldsDto).build();
        log.info("[deletedShopifyProductMetaFields] request body: {}", build);
        Request request = buildPostRequest(deleteProductMetaFieldUrl, build);
        return executeRequest(request, ShopifyDeleteMetaFieldsResponseDto.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.MODIFY_SHOPIFY_PRODUCT_INVENTORY_ITEM)
    public InventoryItemDto modifyShopifyInventoryItem(InventoryItemDto inventoryItemDto) {

        log.info("[modifyShopifyInventoryItem] request body: {}", inventoryItemDto);

        if (null == inventoryItemDto || null == inventoryItemDto.getInventoryItem() || null == inventoryItemDto.getInventoryItem()
            .getId()) {
            log.warn("[modifyShopifyInventoryItem] inventoryItemDto or inventoryItemDto.id is null");
            return null;
        }

        String url = buildProductInventoryItemWithInventoryItemId(modifyProductInventoryItemUrl,
            inventoryItemDto.getInventoryItem().getId());
        Request request = buildPutRequest(url, inventoryItemDto);
        return executeRequest(request, InventoryItemDto.class);
    }

    @ReportMetric(metricsType = MetricsTypeEnum.SET_SHOPIFY_PRODUCT_CHANNEL)
    public void setShopifyChannels(ShopifyProductDto shopifyProduct) {
        log.info("[setShopifyChannels] shopifyProduct: {}", shopifyProduct);

        if (null == shopifyProduct || null == shopifyProduct.getProduct()) {
            log.warn("[setShopifyChannels] shopifyProduct is null");
            return;
        }

        Long shopifyProductId = shopifyProduct.getProduct().getId();
        ShopifyQueryChannelGraphQLDto build = ShopifyQueryChannelGraphQLDto.builder().shopifyProductId(shopifyProductId).build();
        String requestBody = build.buildQueryChannelQraphQL();

        Request request = buildPostRequest(shopifyHost + setProductChannelUrl, requestBody);

        ProductPublicationResponseDto productPublicationResponseDto = executeRequest(request,
            ProductPublicationResponseDto.class);
        Product product = productPublicationResponseDto.getData().getProduct();
        if (null == product || null == product.getResourcePublicationsV2() || CollectionUtils.isEmpty(product.getResourcePublicationsV2().getEdges())) {
            log.error("[setShopifyChannels] product or publications is null, productPublicationResponseDto:{}", productPublicationResponseDto);
            return;
        }

        List<String> allShopifySaleChannels = getAllShopifySaleChannels();

        Map<String, Boolean> isPublishedMap = product.getResourcePublicationsV2().getEdges().stream()
            .map(ProductPublicationResponseDto.ProductData.Product.ResourcePublicationsV2.Edge::getNode)
            .collect(Collectors.toMap(
                node -> node.getPublication().getId(), // key: channel ID
                ProductPublicationResponseDto.ProductData.Product.ResourcePublicationsV2.Edge.Node::isPublished // value: isPublished
            ));

        allShopifySaleChannels.forEach(shopifyChannelId -> {
            Boolean isPublished = isPublishedMap.get(shopifyChannelId);
            log.info("[setShopifyChannels] isPublished: {}, channel: {}", isPublished, shopifyChannelId);
            if (null == isPublished || !isPublished) {
                publishablePublishShopifyProduct(shopifyProductId, shopifyChannelId);
            }
        });
    }

    private ShopifyProductDto variantBindingImage(ShopifyProductDto shopifyProductDto) {

        Image image = shopifyProductDto.getProduct().getImage();

        if (null == image || CollectionUtils.isEmpty(shopifyProductDto.getProduct().getVariants())) {
            log.warn("[variantBindingImage] image is null, shopifyProductDto:{}", shopifyProductDto);
            return shopifyProductDto;
        }

        List<Long> variantIds = shopifyProductDto.getProduct().getVariants().stream()
            .map(Variant::getId)
            .toList();

        VariantImageDto variantImageDto = VariantImageDto.builder()
            .image(Image.builder().id(image.getId()).variantIds(variantIds).build())
            .build();

        log.info("[variantBindingImage] request body: {}", variantImageDto);

        String url = buildShopifyUrlWithProductIdAndImageId(modifyProductImageUrl,
            shopifyProductDto.getProduct().getId(),
            image.getId());

        Request request = buildPutRequest(url, variantImageDto);
        return executeRequest(request, ShopifyProductDto.class);
    }


    private <T> T executeRequest(Request request, Class<T> responseType) {
        RateLimiter rateLimiter = rateLimiterRegistry.rateLimiter("invokeShopifyApi");
        try {
            return rateLimiter.executeSupplier(() -> {
                try {
                    return handleResponse(client.execute(request), responseType);
                } catch (IOException e) {
                    log.error("[ShopifyAdaptor] IO Exception during request execution", e);
                    throw new ImsBusinessException(ErrorCodeEnums.SHOPIFY_REQUEST_ERROR);
                }
            });
        } catch (RequestNotPermitted requestNotPermitted) {
            log.error("[ShopifyAdaptor] Failed to acquire rate limiter");
            throw new ImsBusinessException(ErrorCodeEnums.SHOPIFY_INVOKE_API_TIMEOUT);
        }
    }

    private <T> T handleResponse(Response response, Class<T> responseType) throws IOException {

        if (!response.isSuccessful()) {
            log.error("[handleResponse] Shopify response error: {}", response.body().string());
            throw new ImsBusinessException(ErrorCodeEnums.SHOPIFY_REQUEST_ERROR);
        }
        String string = response.body().string();
        log.info("[handleResponse] Shopify response: {}", string);
        return SerializationUtils.deserialize(string, responseType);
    }

    private void deleteShopifyProductImage(Long productId, Long imageId) {
        log.info("[modifyShopifyProductImage] Removing Image ID: {}", imageId);
        String delUrl = buildShopifyUrlWithProductIdAndImageId(removeProductImageUrl, productId, imageId);
        executeRequest(buildDeleteRequest(delUrl), Void.class);
    }

    private void publishablePublishShopifyProduct(Long productId, String channelId) {
        ShopifyPublicationChannelGraphQLDto shopifyPublicationChannelGraphQLDto = ShopifyPublicationChannelGraphQLDto.builder()
            .shopifyProductId(productId)
            .channelId(channelId)
            .build();
        String requestBody = shopifyPublicationChannelGraphQLDto.buildPublicationChannelQraphQL();
        Request request = buildPostRequest(shopifyHost + setProductChannelUrl, requestBody);

        executeRequest(request, Void.class);
    }

    private Request buildPostRequest(String url, Object body) {
        String accessToken = shopifyAccessTokenBalancer.getNextAccessToken();

        String requestBody;
        if (body instanceof String bodyStr) {
            requestBody = bodyStr;
        } else {
            requestBody = SerializationUtils.serialize(body);
        }
        return new Request.Builder()
            .url(url)
            .addHeader(SHOPIFY_HEADER, accessToken)
            .addHeader(SHOP_API_CALL_LIMIT, COUNT)
            .post(RequestBody.create(requestBody, JSON))
            .build();
    }

    private Request buildPutRequest(String url, Object body) {
        String accessToken = shopifyAccessTokenBalancer.getNextAccessToken();
        return new Request.Builder()
            .url(url)
            .addHeader(SHOPIFY_HEADER, accessToken)
            .addHeader(SHOP_API_CALL_LIMIT, COUNT)
            .put(RequestBody.create(SerializationUtils.serialize(body), JSON))
            .build();
    }

    private Request buildDeleteRequest(String url) {
        String accessToken = shopifyAccessTokenBalancer.getNextAccessToken();

        return new Request.Builder()
            .url(url)
            .addHeader(SHOPIFY_HEADER, accessToken)
            .addHeader(SHOP_API_CALL_LIMIT, COUNT)
            .delete()
            .build();
    }

    private void handleRetryableException(Exception e, String errorMessage) {
        Throwable cause = e.getCause();
        if (cause instanceof RestClientException && (cause.getCause() instanceof SocketTimeoutException
            || cause.getCause() instanceof ConnectTimeoutException)) {
            throw new ServiceRetryableException(errorMessage);
        }
    }

    private String buildShopifyUrlWithProductId(String url, Long productId) {
        return shopifyHost + url.replace("{productId}", productId.toString());
    }

    private String buildShopifyUrlWithProductIdAndImageId(String url, Long productId, Long imageId) {
        return shopifyHost + url.replace("{productId}", productId.toString()).replace("{imageId}", imageId.toString());
    }

    private String buildProductInventoryItemWithInventoryItemId(String url, Long inventoryItemId) {
        return shopifyHost + url.replace("{inventoryItemId}", inventoryItemId.toString());
    }

    private List<String> getAllShopifySaleChannels () {

        List<String> channelIds = channelIdCache.getIfPresent(SALE_CHANNEL_IDS);
        if (CollectionUtils.isNotEmpty(channelIds)) {
            return channelIds;
        } else {
            channelIds = new ArrayList<>();
        }

        String requestBody = ShopifyRequestDtoFactory.buildRequestChannel();
        Request request = buildPostRequest(shopifyHost + setProductChannelUrl, requestBody);

        ShopifyQueryChannelResponseDto shopifyQueryChannelResponseDto = executeRequest(request,
            ShopifyQueryChannelResponseDto.class);

        if (null == shopifyQueryChannelResponseDto.getData() || null == shopifyQueryChannelResponseDto.getData().getPublications()
            || CollectionUtils.isEmpty(shopifyQueryChannelResponseDto.getData().getPublications().getEdges())) {
            log.error("[getAllShopifySaleChannels] shopifyQueryChannelResponseDto is null, shopifyQueryChannelResponseDto:{}",
                shopifyQueryChannelResponseDto);
            return List.of();
        }

        List<Edge> edges = shopifyQueryChannelResponseDto.getData().getPublications().getEdges();
        for (Edge edge : edges) {
            channelIds.add(edge.getNode().getId());
        }

        channelIdCache.put(SALE_CHANNEL_IDS, channelIds);

        return channelIds;
    }

}
