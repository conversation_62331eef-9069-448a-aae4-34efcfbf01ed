package com.mercaso.ims.application.service.impl;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleApplicationServiceImpl implements FinaleApplicationService {

    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final FeatureFlagsManager featureFlagsManager;

    @Async
    @Override
    @Transactional(propagation = REQUIRES_NEW)
    public void syncVendorItem(ItemDto item) {
        if (!featureFlagsManager.isFeatureOn(FeatureFlagKeys.IMS_VENDOR_ITEM_SYNC_FINALE)) {
            log.info("Skip sync vendor item to finale since IMS_VENDOR_ITEM_SYNC_FINALE feature is off");
            return;
        }
        if (item.getAvailabilityStatus().equals(AvailabilityStatus.ARCHIVED.name())) {
            log.info("Skip sync vendor item to finale since item is archived");
            return;
        }
        log.info("Sync vendor item to finale for item={}", item);
        finaleExternalApiAdaptor.createAsSkuNotExist(item.getSkuNumber());
        long inventory = 0L;
        if (item.getBackupVendorItem() != null && item.getBackupVendorItem().getAvailability() != null
            && Boolean.TRUE.equals(item.getBackupVendorItem().getAvailability())) {
            inventory = 9999L;
        }
        VendorItemDto vendorItemDto =
            item.getPrimaryVendorItem() == null ? item.getBackupVendorItem() : item.getPrimaryVendorItem();

        BigDecimal cost = vendorItemDto.isDirectVendorItem() ? vendorItemDto.getCost()
            : vendorItemDto.getBackupCost();

        cost = cost.add(item.getCrvAmount());

        finaleExternalApiAdaptor.updateVendorItem(item.getSkuNumber(), vendorItemDto.getVendorFinaleId(),
            cost, inventory, vendorItemDto.getVendorSkuNumber());
    }
}
