package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import com.mercaso.ims.application.dto.AttachmentDto;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.event.BulkExportRecordsApplicationEvent;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.google.GmailService;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Event listener for handling bulk export records email notifications.
 * This listener processes bulk export completion events and sends email notifications
 * with the exported file as an attachment.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BulkExportRecordsApplicationEventListener {

    private final DocumentApplicationService documentApplicationService;
    private final GmailService gmailService;
    private final BulkExportRecordsService bulkExportRecordsService;

    private static final DateTimeFormatter EMAIL_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Handles bulk export records application events asynchronously.
     * Downloads the exported file and sends it via email to the user.
     *
     * @param event the bulk export records application event
     */
    @Async
    @EventListener
    public void handleBulkExportRecordsApplicationEvent(BulkExportRecordsApplicationEvent event) {
        try {
            // Validate event and payload first
            if (!isValidEvent(event)) {
                log.warn("Invalid event received, skipping processing");
                return;
            }

            log.info("Processing bulk export records event: {}", event.getPayload().getBulkExportRecordsId());

            BulkExportRecordsPayloadDto payload = event.getPayload();
            BulkExportRecordsDto data = payload.getData();

            // Get recipient email
            String recipientEmail = getRecipientEmail();
            if (!StringUtils.hasText(recipientEmail)) {
                log.error("No recipient email found for bulk export records: {}", data.getId());
                return;
            }

            // Process the export and send email
            processExportAndSendEmail(data, recipientEmail);

        } catch (Exception e) {
            log.error("Error processing bulk export records event: {}", e.getMessage(), e);
            // Don't rethrow to prevent event processing failure
        }
    }

    /**
     * Validates the event and its payload.
     */
    private boolean isValidEvent(BulkExportRecordsApplicationEvent event) {
        if (event == null || event.getPayload() == null) {
            log.error("Event or payload is null");
            return false;
        }

        BulkExportRecordsDto data = event.getPayload().getData();
        if (data == null || data.getId() == null || !StringUtils.hasText(data.getFileName())) {
            log.error("Invalid event data: missing required fields");
            return false;
        }

        return true;
    }

    /**
     * Gets the recipient email address.
     */
    private String getRecipientEmail() {
        try {
            return SecurityUtil.getLoginUserEmail();
        } catch (Exception e) {
            log.error("Error getting login user email: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Processes the export file and sends email notification.
     */
    private void processExportAndSendEmail(BulkExportRecordsDto data, String recipientEmail) {
        try {
            // Download the exported file
            String downloadLink = downloadExportFile(data.getFileName());
            if (!StringUtils.hasText(downloadLink)) {
                log.error("Failed to download export file: {}", data.getFileName());
                return;
            }

            // Build email content
            String subject = buildEmailSubject(data.getFileName());
            String body = buildEmailBodyWithLink(data, downloadLink);

            // Send email
            boolean emailSent = sendEmail(recipientEmail, subject, body, null);

            // Update database record
            if (emailSent) {
                updateSendEmailTime(data.getId());
                log.info("Successfully sent bulk export email to: {} for file: {}", recipientEmail, data.getFileName());
            } else {
                log.error("Failed to send bulk export email to: {} for file: {}", recipientEmail, data.getFileName());
            }

        } catch (Exception e) {
            log.error("Error processing export and sending email for file: {}", data.getFileName(), e);
        }
    }

    /**
     * Downloads the export file from document service.
     */
    private String downloadExportFile(String fileName) {
        try {
            log.debug("Downloading export file: {}", fileName);
            return documentApplicationService.getSignedUrl(fileName);
        } catch (Exception e) {
            log.error("Error downloading export file: {}", fileName, e);
            return null;
        }
    }

    /**
     * Builds email subject line.
     */
    private String buildEmailSubject(String fileName) {
        return "Bulk Export Records: " + fileName;
    }

    /**
     * Sends email with attachments.
     */
    private boolean sendEmail(String recipientEmail, String subject, String body, List<AttachmentDto> attachments) {
        try {
            log.debug("Sending email to: {} with subject: {}", recipientEmail, subject);
            return gmailService.sendEmail(recipientEmail, subject, body, attachments);
        } catch (Exception e) {
            log.error("Error sending email to: {}", recipientEmail, e);
            return false;
        }
    }

    /**
     * Updates the send email time in the database.
     */
    private void updateSendEmailTime(UUID recordId) {
        try {
            BulkExportRecords bulkExportRecords = bulkExportRecordsService.findById(recordId);
            if (bulkExportRecords == null) {
                log.error("Bulk Export Records not found for id: {}", recordId);
                throw new ImsBusinessException(ErrorCodeEnums.BULK_EXPORT_RECORDS_NOT_FOUND);
            }

            bulkExportRecords.setSendEmailTime(Instant.now());
            bulkExportRecordsService.update(bulkExportRecords);
            log.debug("Updated send email time for bulk export record: {}", recordId);

        } catch (Exception e) {
            log.error("Error updating send email time for record: {}", recordId, e);
            // Don't rethrow to avoid affecting email sending success
        }
    }

    private String buildEmailBodyWithLink(BulkExportRecordsDto data, String downloadLink) {
        StringBuilder body = new StringBuilder();
        body.append("<html><body>");
        body.append("<p>Dear user,</p>");
        body.append("<p>Your bulk export request has been successfully completed. Please click the link below to download the exported file:</p>");
        body.append("<p><a href=\"").append(downloadLink).append("\">Click here to download the export file</a></p>");
        body.append("<p><strong style=\"color: #d32f2f; font-size: 16px;\">⚠️ Note: This download link is only valid for 1 hour!</strong></p>");

        body.append("<p>Export details:</p>");
        body.append("<ul>");
        body.append("<li>File name: ").append(data.getFileName()).append("</li>");

        if (data.getSearchTime() != null) {
            body.append("<li>Export time: ").append(data.getSearchTime().atZone(java.time.ZoneId.systemDefault())
                    .format(EMAIL_DATE_FORMATTER)).append("</li>");
        }

        if (StringUtils.hasText(data.getExportBy())) {
            body.append("<li>Exported by: ").append(data.getExportBy()).append("</li>");
        }
        body.append("</ul>");

        body.append("<p>Best regards,<br>The Mercaso Omega Team</p>");
        body.append("</body></html>");

        return body.toString();
    }
}
