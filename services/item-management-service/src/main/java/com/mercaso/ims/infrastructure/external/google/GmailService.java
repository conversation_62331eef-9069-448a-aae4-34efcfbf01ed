package com.mercaso.ims.infrastructure.external.google;

import com.mercaso.ims.application.dto.AttachmentDto;
import com.mercaso.ims.application.dto.GmailMessageDto;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.Authenticator;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Part;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Store;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.search.SearchTerm;
import javax.mail.util.ByteArrayDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static javax.mail.Message.RecipientType.TO;


/**
 * Service for Gmail operations including reading emails and sending emails with attachments.
 * This service uses JavaMail API to interact with Gmail via IMAP and SMTP protocols.
 */
@Component
@Slf4j
public class GmailService {

    // IMAP Configuration
    private static final String IMAP_HOST = "imap.gmail.com";
    private static final String IMAP_PORT = "993";
    private static final String IMAP_PROTOCOL = "imaps";

    // SMTP Configuration
    private static final String SMTP_HOST = "smtp.gmail.com";
    private static final String SMTP_PORT = "587";

    // Email Configuration
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String MIME_TYPE_OCTET_STREAM = "application/octet-stream";
    private static final String INBOX_FOLDER = "INBOX";
    private static final String UNKNOWN_ATTACHMENT = "unknown_attachment";
    private static final int BUFFER_SIZE = 8192;

    @Value("${gmail.app_password}")
    private String appPassword;

    @Value("${gmail.user_email}")
    private String userEmail;

    /**
     * Creates an IMAP session for reading emails.
     *
     * @return configured IMAP session
     */
    private Session getImapSession() {
        Properties properties = new Properties();
        properties.put("mail.imap.host", IMAP_HOST);
        properties.put("mail.imap.port", IMAP_PORT);
        properties.put("mail.imap.ssl.enable", "true");
        return Session.getDefaultInstance(properties);
    }

    /**
     * Queries emails from Gmail inbox based on the provided search term.
     *
     * @param searchTerm the search criteria for filtering emails
     * @return list of Gmail message DTOs matching the search criteria
     */
    public List<GmailMessageDto> queryEmails(SearchTerm searchTerm) {
        if (searchTerm == null) {
            log.warn("Search term is null, returning empty list");
            return Collections.emptyList();
        }

        List<GmailMessageDto> emailList = new ArrayList<>();
        Session session = getImapSession();

        try (Store store = session.getStore(IMAP_PROTOCOL)) {
            store.connect(IMAP_HOST, userEmail, appPassword);

            try (Folder inbox = store.getFolder(INBOX_FOLDER)) {
                inbox.open(Folder.READ_ONLY);

                Message[] messages = inbox.search(searchTerm);
                log.info("Found {} messages matching search criteria", messages.length);

                for (Message message : messages) {
                    try {
                        GmailMessageDto emailDto = buildGmailMessageDto(message);
                        emailList.add(emailDto);
                    } catch (Exception e) {
                        log.error("Error processing message: {}", e.getMessage(), e);
                        // Continue processing other messages
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error querying emails: {}", e.getMessage(), e);
        }

        return emailList;
    }

    /**
     * Builds a GmailMessageDto from a JavaMail Message.
     *
     * @param message the JavaMail message
     * @return the Gmail message DTO
     * @throws MessagingException if there's an error processing the message
     */
    private GmailMessageDto buildGmailMessageDto(Message message) throws MessagingException {
        return GmailMessageDto.builder()
            .subject(message.getSubject())
            .from(Arrays.toString(message.getFrom()))
            .receivedDate(message.getReceivedDate() != null ? message.getReceivedDate().toInstant() : null)
            .attachments(getAttachments(message))
            .build();
    }

    private List<AttachmentDto> getAttachments(Message message) {
        List<AttachmentDto> attachmentDtoList = new ArrayList<>();
        try {
            if (message.isMimeType("multipart/*")) {
                MimeMultipart multipart = (MimeMultipart) message.getContent();
                for (int i = 0; i < multipart.getCount(); i++) {
                    MimeBodyPart part = (MimeBodyPart) multipart.getBodyPart(i);
                    if (Part.ATTACHMENT.equalsIgnoreCase(part.getDisposition()) || part.getFileName() != null) {
                        try (InputStream is = part.getInputStream();
                            ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
                            byte[] data = new byte[BUFFER_SIZE];
                            int bytesRead;
                            while ((bytesRead = is.read(data, 0, data.length)) != -1) {
                                buffer.write(data, 0, bytesRead);
                            }
                            attachmentDtoList.add(
                                AttachmentDto.builder()
                                    .filename(part.getFileName() != null ? part.getFileName() : UNKNOWN_ATTACHMENT)
                                    .fileData(buffer.toByteArray())
                                    .build()
                            );
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting attachments", e);
        }
        return attachmentDtoList;
    }

    private Session createSmtpSession() {
        Properties properties = new Properties();
        properties.put("mail.smtp.host", SMTP_HOST);
        properties.put("mail.smtp.port", SMTP_PORT);
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true");

        return Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(userEmail, appPassword);
            }
        });
    }

    private MimeMultipart createEmailContent(String body, List<AttachmentDto> attachments) throws Exception {
        MimeMultipart multipart = new MimeMultipart();

        // Add message body
        MimeBodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setText(body, CHARSET_UTF8);
        multipart.addBodyPart(messageBodyPart);

        // Add attachments
        if (attachments != null && !attachments.isEmpty()) {
            for (AttachmentDto attachment : attachments) {
                if (attachment.getFileData() != null && attachment.getFilename() != null) {
                    MimeBodyPart attachmentPart = new MimeBodyPart();
                    DataSource source = new ByteArrayDataSource(
                            attachment.getFileData(), MIME_TYPE_OCTET_STREAM);
                    attachmentPart.setDataHandler(new DataHandler(source));
                    attachmentPart.setFileName(attachment.getFilename());
                    multipart.addBodyPart(attachmentPart);
                }
            }
        }

        return multipart;
    }

    public boolean sendEmail(String to, String subject, String body, List<AttachmentDto> attachments) {
        if (to == null || to.trim().isEmpty()) {
            log.error("Email recipient cannot be empty");
            return false;
        }
        if (subject == null || subject.trim().isEmpty()) {
            log.error("Email subject cannot be empty");
            return false;
        }
        if (body == null || body.trim().isEmpty()) {
            log.error("Email body cannot be empty");
            return false;
        }

        try {
            Session session = createSmtpSession();
            Message message = new MimeMessage(session);

            message.setFrom(new InternetAddress(userEmail));
            message.setRecipients(TO, InternetAddress.parse(to));
            message.setSubject(subject);
            message.setContent(createEmailContent(body, attachments));

            Transport.send(message);

            log.info("Email sent successfully to: {}, subject: {}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("Failed to send email to: {}, subject: {}, error: {}", to, subject, e.getMessage(), e);
            return false;
        }
    }

}
