package com.mercaso.ims.domain.itemadjustmentrequestdetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ItemAdjustmentFailureReason {
    ITEM_NOT_FOUND("Item not found"),
    ITEM_INVENTORY_NOT_FOUND("Item Inventory not found"),
    VENDOR_NOT_FOUND("Vendor not found"),
    BACKUP_VENDOR_NOT_FOUND("Backup vendor not found"),
    JIT_VENDOR_NOT_FOUND("JIT vendor not found"),
    PRIMARY_VENDOR_NOT_FOUND("Primary Vendor not found"),
    PRIMARY_VENDOR_AND_BACKUP_VENDOR_CANNOT_BE_EMPTY("primary Direct and primary JIT cannot be empty at the same time"),
    PRIMARY_VENDOR_ITEM_COST_IS_REQUIRED("Primary Vendor Item Cost is required"),
    PRIMARY_DIRECT_VENDOR_ITEM_COST_IS_REQUIRED("Primary Direct Vendor Item Cost is required"),
    PRIMARY_JIT_VENDOR_ITEM_COST_IS_REQUIRED("Primary JIT Vendor Item Cost is required"),
    INVALID_VENDOR_NAME("Invalid Vendor Name"),
    INVALID_CATEGORY("Invalid Category"),
    VENDOR_ALREADY_EXISTS("Vendor already exists"),
    TAXONOMY_IS_REQUIRED("Taxonomy is required"),
    DEPARTMENT_NOT_FOUND("Department not found"),
    CATEGORY_ALREADY_EXISTS("Category already exists"),
    CATEGORY_NOT_FOUND("Category not found"),
    CATEGORY_NOT_MATCH("Category not match"),
    SUB_CATEGORY_NOT_MATCH("Sub Category not match"),
    CLASS_NOT_MATCH("Class not match"),
    SUB_CATEGORY_NOT_FOUND("Sub Category not found"),
    ITEM_ALREADY_EXISTS("Item already exists"),
    ITEM_ALREADY_DELETED("Item already deleted"),
    INVALID_REG_PRICE("Invalid Reg Price"),
    INVALID_PACK_SIZE("Invalid pack size"),
    INVALID_ITEM_DESCRIPTION("Invalid item description"),
    NEW_DESCRIPTION_IS_REQUIRED("New description is required"),
    INVALID_BOTTLE_SIZE("Invalid  Bottle size"),
    INVALID_BOTTLE_UNIT("Invalid Bottle unit"),
    INVALID_COMPANY_ID("Invalid Company Id"),
    CANT_CHANGE_PRICE_SINCE_ITEM_IN_PRICE_GROUP("Can't change price since item in price group"),
    INVALID_LOCATION_ID("Invalid Location Id"),
    INVALID_STATUS("Invalid Status"),
    INVALID_ITEM_SIZE("Invalid item size"),
    DESCRIPTION_TOO_LONG("Item description too long"),
    VENDOR_NAME_TOO_LONG("Vendor name too long"),
    VENDOR_NAME_IS_REQUIRED("Vendor name is required"),
    VENDOR_ITEM_TYPE_REQUIRED("Vendor Item Type is required"),
    VENDOR_ITEM_INFO_IS_REQUIRED("Item Number, Cost, Secondary Cost, Aisle, At least one is required"),
    DIRECT_VENDOR_ITEM_COST_IS_REQUIRED("Direct Vendor Item Cost is required"),
    JIT_VENDOR_ITEM_COST_IS_REQUIRED("JIT Vendor Item Cost is required"),
    DIRECT_JIT_VENDOR_ITEM_COST_IS_REQUIRED("Direct and JIT Vendor Item Cost is required"),
    INVALID_VENDOR_ITEM_TYPE("Invalid Vendor Item Type"),
    INVALID_CRV_FLAG_FOR_DEPARTMENT("Invalid crv flag for department"),
    INVALID_IMAGE_URL("Invalid Image url"),
    PROCESS_IMAGE_URL_ERROR("Process Image url error"),
    INVALID_UPC("Invalid UPC number"),
    INVALID_BACKUP_VENDOR("Invalid backup vendor"),
    INVALID_JIT_VENDOR("Invalid JIT vendor"),
    PRIMARY_VENDOR_ITEM_COST_MUST_BE_GREATER_THAN_ZERO("Primary vendor Item cost must be greater than 0"),
    PRIMARY_DIRECT_VENDOR_ITEM_COST_MUST_BE_GREATER_THAN_ZERO("Primary Direct vendor Item cost must be greater than 0"),
    PRIMARY_JIT_VENDOR_ITEM_COST_MUST_BE_GREATER_THAN_ZERO("Primary JIT vendor Item cost must be greater than 0"),
    INVALID_WEIGHT_UNIT("Invalid Weight unit"),
    SKU_IS_REQUIRED("SKU is required"),
    INVALID_SKU_FORMAT("SKU cannot contain spaces or commas"),
    ;
    private final String reason;

}
