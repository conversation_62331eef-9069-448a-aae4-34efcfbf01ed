package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.BaseDto;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategoryDeletedDto extends BaseDto {

    private List<UUID> categoryHierarchyIds;

}
