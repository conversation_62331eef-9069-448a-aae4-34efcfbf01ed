package com.mercaso.ims.application.service;


import jakarta.servlet.http.HttpServletResponse;
import java.time.Instant;

public interface MerchandiseReportService {

    void getMerchandiseReportFile();

    void getChangeResultMerchandiseReportFile(HttpServletResponse httpResponse, Instant updatedAtStartTime, Instant updatedAtEndTime);

    void downloadFilteredMerchandiseReport(String customFilter);
}

