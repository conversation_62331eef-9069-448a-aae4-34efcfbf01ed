package com.mercaso.partnerinsight.resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.config.SecurityContextUtilWrapper;
import com.mercaso.partnerinsight.dto.TenantStoreSalesInfoDto;
import com.mercaso.partnerinsight.entity.TenantStoreSalesInfo;
import com.mercaso.partnerinsight.repository.TenantStoreSalesInfoRepository;
import com.mercaso.partnerinsight.utils.TenantStoreSalesInfoUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

@Execution(ExecutionMode.SAME_THREAD)
class TenantStoreSalesInfoRestApiIT extends AbstractIT {

    @Autowired
    private TenantStoreSalesInfoRepository tenantStoreSalesInfoRepository;

    @MockBean
    protected SecurityContextUtilWrapper securityContextUtilWrapper;

    @Test
    void shouldSuccessWhenFindStoreSalesInfoByTenantIdAndStoreId() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String storeId = "STORE_" + RandomStringUtils.randomAlphabetic(5);
        
        Instant currentMonth = Instant.now().truncatedTo(ChronoUnit.DAYS);
        Instant lastMonth = currentMonth.minus(30, ChronoUnit.DAYS);

        TenantStoreSalesInfo salesInfo1 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 200L, currentMonth);
        TenantStoreSalesInfo salesInfo2 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 150L, lastMonth);

        tenantStoreSalesInfoRepository.save(salesInfo1);
        tenantStoreSalesInfoRepository.save(salesInfo2);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoRestApiUtil.findStoreSalesInfoByTenantIdAndStoreId(storeId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify the results are ordered by sales month descending
        assertEquals(currentMonth, result.get(0).getSalesMonth());
        assertEquals(lastMonth, result.get(1).getSalesMonth());
        assertEquals(200L, result.get(0).getSalesQty());
        assertEquals(150L, result.get(1).getSalesQty());
    }

    @Test
    void shouldReturnEmptyListWhenStoreIdNotFound() throws Exception {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String nonExistentStoreId = "NON_EXISTENT_STORE_ID";

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId);

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoRestApiUtil.findStoreSalesInfoByTenantIdAndStoreId(nonExistentStoreId);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void shouldReturnOnlyDataForCorrectTenant() throws Exception {
        // Given
        String tenantId1 = RandomStringUtils.randomAlphabetic(5);
        String tenantId2 = RandomStringUtils.randomAlphabetic(5);
        String storeId = "STORE_" + RandomStringUtils.randomAlphabetic(5);

        TenantStoreSalesInfo salesInfoTenant1 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId1, storeId);
        TenantStoreSalesInfo salesInfoTenant2 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId2, storeId);

        tenantStoreSalesInfoRepository.save(salesInfoTenant1);
        tenantStoreSalesInfoRepository.save(salesInfoTenant2);

        when(securityContextUtilWrapper.getLoginUserTenantId()).thenReturn(tenantId1);

        // When
        List<TenantStoreSalesInfoDto> result = tenantStoreSalesInfoRestApiUtil.findStoreSalesInfoByTenantIdAndStoreId(storeId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(tenantId1, result.get(0).getTenantId());
    }
}
