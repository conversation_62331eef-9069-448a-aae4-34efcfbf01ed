package com.mercaso.partnerinsight.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.partnerinsight.AbstractIT;
import com.mercaso.partnerinsight.entity.TenantStoreSalesInfo;
import com.mercaso.partnerinsight.utils.TenantStoreSalesInfoUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class TenantStoreSalesInfoRepositoryIT extends AbstractIT {

    @Autowired
    private TenantStoreSalesInfoRepository tenantStoreSalesInfoRepository;

    @Test
    void shouldSuccessWhenFindAllByTenantIdAndStoreId() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String storeId = "STORE_" + RandomStringUtils.randomAlphabetic(5);
        
        Instant currentMonth = Instant.now().truncatedTo(ChronoUnit.DAYS);
        Instant lastMonth = currentMonth.minus(30, ChronoUnit.DAYS);

        TenantStoreSalesInfo salesInfo1 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 200L, currentMonth);
        TenantStoreSalesInfo salesInfo2 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 150L, lastMonth);

        tenantStoreSalesInfoRepository.save(salesInfo1);
        tenantStoreSalesInfoRepository.save(salesInfo2);

        // When
        List<TenantStoreSalesInfo> result = tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthDesc(tenantId, storeId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(sales -> sales.getSalesQty().equals(200L)));
        assertTrue(result.stream().anyMatch(sales -> sales.getSalesQty().equals(150L)));
    }

    @Test
    void shouldReturnEmptyListWhenTenantIdNotFound() {
        // Given
        String nonExistentTenantId = "NON_EXISTENT_TENANT_ID";
        String storeId = "STORE_" + RandomStringUtils.randomAlphabetic(5);

        // When
        List<TenantStoreSalesInfo> result = tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthDesc(nonExistentTenantId, storeId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnEmptyListWhenStoreIdNotFound() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String nonExistentStoreId = "NON_EXISTENT_STORE_ID";

        // When
        List<TenantStoreSalesInfo> result = tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthDesc(tenantId, nonExistentStoreId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnOnlyDataForCorrectTenantAndStore() {
        // Given
        String tenantId1 = RandomStringUtils.randomAlphabetic(5);
        String tenantId2 = RandomStringUtils.randomAlphabetic(5);
        String storeId1 = "STORE_1_" + RandomStringUtils.randomAlphabetic(5);
        String storeId2 = "STORE_2_" + RandomStringUtils.randomAlphabetic(5);

        TenantStoreSalesInfo salesInfoTenant1Store1 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId1, storeId1);
        TenantStoreSalesInfo salesInfoTenant2Store1 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId2, storeId1);
        TenantStoreSalesInfo salesInfoTenant1Store2 = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId1, storeId2);

        tenantStoreSalesInfoRepository.save(salesInfoTenant1Store1);
        tenantStoreSalesInfoRepository.save(salesInfoTenant2Store1);
        tenantStoreSalesInfoRepository.save(salesInfoTenant1Store2);

        // When
        List<TenantStoreSalesInfo> result = tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthDesc(tenantId1, storeId1);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(tenantId1, result.get(0).getTenantId());
        assertEquals(storeId1, result.get(0).getStoreId());
    }

    @Test
    void shouldReturnDataOrderedBySalesMonthDesc() {
        // Given
        String tenantId = RandomStringUtils.randomAlphabetic(5);
        String storeId = "STORE_" + RandomStringUtils.randomAlphabetic(5);
        
        Instant january = Instant.parse("2024-01-01T00:00:00Z");
        Instant february = Instant.parse("2024-02-01T00:00:00Z");
        Instant march = Instant.parse("2024-03-01T00:00:00Z");

        TenantStoreSalesInfo salesInfoJan = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 100L, january);
        TenantStoreSalesInfo salesInfoMar = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 300L, march);
        TenantStoreSalesInfo salesInfoFeb = TenantStoreSalesInfoUtils.buildTenantStoreSalesInfo(tenantId, storeId, 200L, february);

        // Save in random order
        tenantStoreSalesInfoRepository.save(salesInfoJan);
        tenantStoreSalesInfoRepository.save(salesInfoMar);
        tenantStoreSalesInfoRepository.save(salesInfoFeb);

        // When
        List<TenantStoreSalesInfo> result = tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthDesc(tenantId, storeId);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify descending order by sales month (March -> February -> January)
        assertEquals(march, result.get(0).getSalesMonth());
        assertEquals(300L, result.get(0).getSalesQty());
        
        assertEquals(february, result.get(1).getSalesMonth());
        assertEquals(200L, result.get(1).getSalesQty());
        
        assertEquals(january, result.get(2).getSalesMonth());
        assertEquals(100L, result.get(2).getSalesQty());
    }
}
