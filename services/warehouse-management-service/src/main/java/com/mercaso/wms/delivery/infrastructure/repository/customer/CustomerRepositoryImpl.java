package com.mercaso.wms.delivery.infrastructure.repository.customer;

import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.customer.CustomerRepository;
import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.CustomerJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.dataobject.CustomerDo;
import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.mapper.CustomerDoMapper;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CustomerRepositoryImpl implements CustomerRepository {

    private final CustomerDoMapper mapper;
    private final CustomerJpaDao jpaDao;

    @Override
    public Customer save(Customer domain) {
        CustomerDo customerDo = mapper.domainToDo(domain);
        return mapper.doToDomain(jpaDao.save(customerDo));
    }

    @Override
    public Customer findById(UUID id) {
        Optional<CustomerDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public Customer update(Customer domain) {
        CustomerDo customerDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == customerDo) {
            throw new WmsBusinessException("Customer not found.");
        }
        CustomerDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, customerDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(customerDo));
    }

    @Override
    public Customer findByEmail(String email) {
        return mapper.doToDomain(jpaDao.findByEmail(email));
    }

    @Override
    public Customer findByExternalId(String externalId) {
        return mapper.doToDomain(jpaDao.findByExternalId(externalId));
    }
}