package com.mercaso.wms.delivery.infrastructure.external.routemanage;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.config.RouteManagerProperties;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverRequest;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoutesResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.CurrentRoutes;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.DriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEvent;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEventRequest;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEventResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.exceptions.RmRequestValidationException;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@RequiredArgsConstructor
@Slf4j
public class RouteManagerAdaptor {

    // API Path Constants
    private static final String API_V1 = "/api/v1";
    private static final String API_V2 = "/api/v2";
    private static final String TERRITORIES = "/territories";
    private static final String DRIVERS = "/drivers";
    private static final String APPROVED_ROUTES = "/approved/routes";
    private static final String EXECUTION_EVENTS = "/toa/executionevents";
    private static final String TOA_ROUTES = "/toa/routes";
    private static final String TOO_MANY_REQUESTS_MESSAGE = "There are too many requests per minutes, please try again later.";
    private static final String TOO_MANY_REQUESTS_CODE = "code=429";

    private final RouteManagerProperties routeManagerProperties;
    private final FeatureFlagsManager featureFlagsManager;

    /**
     * Fetches approved routes for the given dates.
     *
     * @param dates format: {@link com.mercaso.wms.infrastructure.utils.DateUtils#RM_DATE_TO_STRING_FORMATTER}
     * @return ApprovedRoutesResponse
     */
    public ApprovedRoutesResponse getApprovedRoutesV2(List<String> dates) {
        return executeWithErrorHandling(
            () -> HttpClientUtils.executeGetRequest(
                buildApprovedRoutesUri(dates),
                buildHeaders(),
                ApprovedRoutesResponse.class
            ),
            ApprovedRoutesResponse::new,
            "Fetching approved routes for dates: {}",
            "Error while fetching approved routes for dates: {}",
            dates
        );
    }

    /**
     * Fetches an approved route with the specified ID.
     *
     * @param routeId the route ID to fetch
     * @return ApprovedRoute or null if not found
     */
    public ApprovedRoute getApprovedRoute(String routeId) {
        return executeWithErrorHandling(
            () -> HttpClientUtils.executeGetRequest(
                buildGetApprovedRoutesUri(routeId),
                buildHeaders(),
                ApprovedRoute.class
            ),
            () -> null,
            "Fetching approved route with ID: {}",
            "Error while get approved route with ID: {}",
            routeId
        );
    }

    public AddDriverResponse addDriver(AddDriverRequest request) {
        try {
            request.setPassword(routeManagerProperties.getDefaultSecretKey());
            String body = SerializationUtils.serialize(request);
            log.info("Adding driver with name: {}, email: {}", request.getName(), request.getEmail());
            return HttpClientUtils.executePostRequest(buildAddDriverUri(), body, buildHeaders(), AddDriverResponse.class);
        } catch (RmRequestValidationException e) {
            log.error("Driver validation failed: {}", e.getMessage());
            throw e;
        } catch (IOException e) {
            handleIoException(e, "Error while adding driver with name: {}, email: {}", request.getName(), request.getEmail());
            return new AddDriverResponse();
        }
    }

    public ExecutionEventResponse sendExecutionEvents(List<ExecutionEvent> events) {
        if (CollectionUtils.isEmpty(events)) {
            log.warn("No execution events to send");
            return createEmptyResponse();
        }

        log.info("Preparing to send {} execution events", events.size());

        if (!isExecutionEventFeatureEnabled()) {
            return createEmptyResponse();
        }

        try {
            return doSendExecutionEvents(events);
        } catch (Exception e) {
            log.warn("Error occurred while sending execution events", e);
            return createEmptyResponse();
        }
    }

    private boolean isExecutionEventFeatureEnabled() {
        boolean isEnabled = featureFlagsManager.isFeatureOn(FeatureFlagKeys.SEND_EXECUTION_EVENTS_TO_RM);
        if (!isEnabled) {
            log.info("Feature flag SEND_EXECUTION_EVENTS_TO_RM is off, skipping execution events");
        }
        return isEnabled;
    }

    private ExecutionEventResponse doSendExecutionEvents(List<ExecutionEvent> events) throws IOException {
        ExecutionEventRequest request = new ExecutionEventRequest();
        request.setEvents(events);

        String uri = buildExecutionEventsUri();
        log.debug("Sending execution events to URI: {}", uri);

        ExecutionEventResponse response = HttpClientUtils.executePostRequest(
            uri,
            request,
            buildHeaders(),
            ExecutionEventResponse.class
        );

        log.info("Successfully sent execution events, received requestId: {}", response.getRequestId());
        return response;
    }

    private ExecutionEventResponse createEmptyResponse() {
        return new ExecutionEventResponse();
    }

    public List<Driver> listDrivers() {
        return executeWithErrorHandling(
            () -> HttpClientUtils.executeGetRequest(
                buildDriverListUri(),
                buildHeaders(),
                DriverResponse.class
            ).getDrivers().values().stream().toList(),
            List::of,
            "Fetching driver list",
            "Error while fetching driver list",
            null
        );
    }

    /**
     * Fetches a route details
     *
     * @param routeId the route ID
     * @return Route details or null if not found
     */
    public CurrentRoutes getCurrentRoute(String routeId) {
        return executeWithErrorHandling(
            () -> HttpClientUtils.executeGetRequest(
                buildRouteUri(routeId),
                buildHeaders(),
                CurrentRoutes.class
            ),
            () -> null,
            "Fetching route with routeId: {}",
            "Error while fetching route with routeId: {}",
            routeId
        );
    }

    /**
     * Generic method to execute HTTP request with standardized error handling.
     *
     * @param requestFunction The function to execute the HTTP request
     * @param defaultSupplier Supplier for default value in case of error
     * @param logInfoMessage Info log message
     * @param logErrorMessage Error log message
     * @param logParam Parameter for log messages
     * @return The result of the HTTP request or default value if error occurs
     */
    private <T, P> T executeWithErrorHandling(
        HttpRequestFunction<T> requestFunction,
        Supplier<T> defaultSupplier,
        String logInfoMessage,
        String logErrorMessage,
        P logParam) {
        try {
            if (logParam != null) {
                log.info(logInfoMessage, logParam);
            } else {
                log.info(logInfoMessage);
            }
            return requestFunction.execute();
        } catch (IOException e) {
            if (logParam != null) {
                handleIoException(e, logErrorMessage, logParam);
            } else {
                handleIoException(e, logErrorMessage);
            }
            return defaultSupplier.get();
        }
    }

    /**
     * Handles IOException with standardized error handling for 429 Too Many Requests.
     */
    private void handleIoException(IOException e, String errorMessage, Object... params) {
        boolean tooManyRequest = e.getMessage().contains(TOO_MANY_REQUESTS_CODE);
        if (tooManyRequest) {
            throw new DeliveryBadRequestException(TOO_MANY_REQUESTS_MESSAGE);
        }
        log.error(errorMessage, params, e);
    }

    /**
     * Functional interface for HTTP request execution that can throw IOException.
     */
    @FunctionalInterface
    private interface HttpRequestFunction<T> {

        T execute() throws IOException;
    }

    /**
     * Creates base URI builder with common path segments.
     *
     * @param apiVersion API version path (API_V1 or API_V2)
     * @return UriComponentsBuilder with base path configured
     */
    private UriComponentsBuilder createBaseUriBuilder(String apiVersion) {
        return UriComponentsBuilder.fromHttpUrl(routeManagerProperties.getBaseUrl())
            .path(apiVersion)
            .path(TERRITORIES)
            .pathSegment(routeManagerProperties.getTerritoryId());
    }

    private String buildDriverListUri() {
        return createBaseUriBuilder(API_V1)
            .path(DRIVERS)
            .build()
            .toUriString();
    }

    private String buildApprovedRoutesUri(List<String> dates) {
        UriComponentsBuilder builder = createBaseUriBuilder(API_V2)
            .path(APPROVED_ROUTES);

        handleQueryParams(builder, dates);
        return builder.build().toUriString();
    }

    private String buildGetApprovedRoutesUri(String routeId) {
        return createBaseUriBuilder(API_V2)
            .path(APPROVED_ROUTES)
            .path("/" + routeId)
            .build()
            .toUriString();
    }

    private void handleQueryParams(UriComponentsBuilder builder, List<String> dates) {
        builder.queryParam("dates", String.join(",", dates));
    }

    private String buildAddDriverUri() {
        return createBaseUriBuilder(API_V1)
            .path(DRIVERS)
            .build()
            .toUriString();
    }

    private String buildExecutionEventsUri() {
        return createBaseUriBuilder(API_V1)
            .path(EXECUTION_EVENTS)
            .build()
            .toUriString();
    }

    private String buildRouteUri(String routeId) {
        return createBaseUriBuilder(API_V1)
            .path(TOA_ROUTES)
            .pathSegment(routeId)
            .build()
            .toUriString();
    }

    private Map<String, String> buildHeaders() {
        return Map.of("Content-Type", "application/json", "X-WorkWave-Key", routeManagerProperties.getApiKey());
    }
}
