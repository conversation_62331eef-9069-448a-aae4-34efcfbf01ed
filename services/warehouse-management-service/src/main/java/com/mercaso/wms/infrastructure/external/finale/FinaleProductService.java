package com.mercaso.wms.infrastructure.external.finale;

import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.infrastructure.exception.ServiceRetryableException;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.CreatePurchaseOrderDto;
import com.mercaso.wms.infrastructure.external.finale.dto.CreateShipmentDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleFacilityDataResponseDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleFacilityDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleProductDataResponseDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferRequestDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferResponseDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.PurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderDto;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ShipmentResponse;
import com.mercaso.wms.infrastructure.external.finale.mapper.FinaleAvailableStockMapper;
import com.mercaso.wms.infrastructure.external.finale.mapper.FinaleFacilityMapper;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.ConnectTimeoutException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@RequiredArgsConstructor
@Slf4j
public class FinaleProductService {

    private final FinaleAvailableStockMapper finaleAvailableStockMapper;

    private final FinaleConfigProperties properties;

    private final FinaleFacilityMapper finaleFacilityMapper;

    private static final String AUTH_HEADER = "Basic ";

    public static final String QUERY = "query";

    private static final String FACILITY_URL_PREFIX = "/api/facility/";

    private static final String PRODUCT_URL_PREFIX = "/api/product/";



    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public List<FinaleAvailableStockDto> getAvailableStock(int pageSize) {
        try {
            Optional<FinaleProductDataResponseDto> finaleProductDataResponseDto = fetchProductData(properties.getMfcFacilityUrl(),
                pageSize);

            return finaleProductDataResponseDto.map(productDataResponseDto -> productDataResponseDto
                .getData()
                .getProductViewConnection()
                .getEdges()
                .stream()
                .map(finaleAvailableStockMapper::toFinaleAvailableStockDto)
                .toList()).orElseGet(List::of);
        } catch (Exception e) {
            Throwable t = e.getCause();
            if (t instanceof RestClientException && (t.getCause() instanceof SocketTimeoutException
                || t.getCause() instanceof ConnectTimeoutException)) {
                throw new ServiceRetryableException("Exception calling get products %s");
            }
            log.warn("Error fetching product data from Finale", e);
        }
        return List.of();
    }

    public Optional<FinaleProductDataResponseDto> fetchProductData(String facilityUrl, int pageSize) throws Exception {
        String jsonBody = buildRequestBody(facilityUrl, pageSize);

        Map<String, String> headers = buildHeaders();

        FinaleProductDataResponseDto finaleProductDataResponseDto = HttpClientUtils.executePostRequest(properties.getGraphqlApiUrl(),
            jsonBody,
            headers,
            FinaleProductDataResponseDto.class);
        return Optional.of(finaleProductDataResponseDto);
    }

    private static String buildRequestBody(String facilityUrl, int pageSize) throws IOException {

        Map<String, Object> stringRequestBody = getStringRequestBody(pageSize);
        String queryString = (String) stringRequestBody.get(QUERY);

        queryString = queryString.replace("facilityUrlList: \"\"", "facilityUrlList: \"" + facilityUrl + "\"");
        queryString = queryString.replace("facilityUrlList: [\"\"]", "facilityUrlList: [\"" + facilityUrl + "\"]");

        stringRequestBody.put(QUERY, queryString);

        return SerializationUtils.serialize(stringRequestBody);
    }

    private Map<String, String> buildHeaders() {
        return Map.of(
            "Content-Type", "application/json",
            "Authorization", AUTH_HEADER + properties.getToken()
        );
    }

    private static Map<String, Object> getStringRequestBody(int pageSize) {
        Map<String, Object> map = new HashMap<>();
        map.put("operationName", "Rows");
        map.put("variables", Map.of("first", pageSize
        ));
        map.put(QUERY, """
            query Rows($after: String, $first: Int) {
              productViewConnection(status: ["PRODUCT_ACTIVE"], stock: {minExclusive: 0, count: totalUnits, stockType: onHand, facilityUrlList: [""]}, after: $after, first: $first, sort: [{field: "recordLastUpdated", mode: "desc"}]) {
                summary {
                  metrics {
                    count
                  }
                }
                edges {
                  node(timezone: "") {
                    name: description
                    stockItemsOnHand: stockItem(type: "STOCK_ITEM_ON_HAND", first: 40) {
                      edges {
                        node {
                          description: title
                          packing: packing
                          quantityOnHand: quantity(formatter: "none")
                          sublocation {
                            facilityUrl
                            name
                          }
                        }
                      }
                      summary {
                        metrics {
                          count
                        }
                      }
                    }
                    mfcQoh: stockOnHand(count: openQuantity, facilityUrlList: "")
                    sku: productId
                    reservationsQoh: stockReservationsUnits
                    recordLastUpdated: recordLastUpdated
                    stockSublocations: stockSublocations
                    productUrl: productUrl
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
            """);

        return map;
    }

    public List<FinaleFacilityDto> getFinaleFacilityData() {
        Optional<FinaleFacilityDataResponseDto> finaleFacilityDataResponseDto = fetchFacilityData();
        if (finaleFacilityDataResponseDto.isEmpty()) {
            return new ArrayList<>();
        }

        FinaleFacilityDataResponseDto finaleFacilityDataResponse = finaleFacilityDataResponseDto.get();
        return finaleFacilityMapper.toFinaleFacilityDtoList(finaleFacilityDataResponse, properties.getMfcFacilityUrl());
    }

    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public Optional<FinaleFacilityDataResponseDto> fetchFacilityData() {
        try {
            Map<String, String> headers = buildHeaders();
            return Optional.ofNullable(HttpClientUtils.executeGetRequest(
                properties.getFacilityUrl(),
                headers,
                FinaleFacilityDataResponseDto.class
            ));
        } catch (Exception e) {
            Throwable t = e.getCause();
            if (t instanceof RestClientException && (t.getCause() instanceof SocketTimeoutException
                || t.getCause() instanceof ConnectTimeoutException)) {
                throw new ServiceRetryableException("Exception calling get facilities");
            }
            log.error("Error fetching facilities data from Finale", e);
        }
        return Optional.empty();
    }

    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public FinaleTransferResponseDto quickTransferStock(String skuNumber,
        int quantity,
        Location from,
        Location to,
        String taskNumber) {
        return quickTransferStock(skuNumber, quantity, from.getName(), to.getName(), from.getFinaleId(), to.getFinaleId(), taskNumber);
    }

    public FinaleTransferResponseDto quickTransferStock(String skuNumber,
        int quantity,
        String from,
        String to,
        String fromId,
        String toId,
        String taskNumber) {
        try {
            Map<String, String> headers = buildHeaders();
            StringBuilder comments = new StringBuilder();
            comments.append( "Transfer from ");
            comments.append(from);
            comments.append(" to ");
            comments.append(to);
            comments.append(" for ");
            comments.append(skuNumber);
            comments.append(" with quantity ");
            comments.append(quantity);
            if (StringUtils.isNotEmpty(taskNumber)) {
                comments.append(" by system. Task Number: ");
                comments.append(taskNumber);
            }

            FinaleTransferRequestDto transferRequestDto = FinaleTransferRequestDto.builder()
                    .productUrl("/" + properties.getDomain() + PRODUCT_URL_PREFIX + skuNumber)
                    .facilityUrlFrom("/" + properties.getDomain() + FACILITY_URL_PREFIX + fromId)
                    .facilityUrlTo("/" + properties.getDomain() + FACILITY_URL_PREFIX + toId)
                    .quantity(quantity)
                    .receiveDate(LocalDateTime.now())
                    .sendDate(LocalDateTime.now())
                    .generalComments(comments.toString())
                    .build();

            return HttpClientUtils.executePostRequest(properties.getTransferUrl(),
                    SerializationUtils.serialize(transferRequestDto),
                    headers,
                    FinaleTransferResponseDto.class);
        } catch (Exception e) {
            Throwable t = e.getCause();
            if (t instanceof RestClientException && (t.getCause() instanceof SocketTimeoutException
                    || t.getCause() instanceof ConnectTimeoutException)) {
                throw new ServiceRetryableException("Exception calling get facilities");
            }
            log.warn("Error transfer stock for sku {} from {} to {} with {} qty",
                    skuNumber,
                    from,
                    to,
                    quantity,
                    e);
        }
        return null;
    }

    public FinaleTransferShipmentResponse createTransferShipment(FinaleTransferShipmentDto finaleTransferShipmentDto) {
        try {
            Map<String, String> headers = buildHeaders();

            return HttpClientUtils.executePostRequest(properties.getCreateTransferShipmentUrl(),
                SerializationUtils.serialize(finaleTransferShipmentDto),
                headers,
                FinaleTransferShipmentResponse.class);
        } catch (Exception e) {
            log.error("[createTransferShipment] Error creating transfer shipment", e);
        }
        return null;
    }

    public FinaleTransferShipmentResponse shipTransferShipment(String shipmentId) {
        try {
            Map<String, String> headers = buildHeaders();
            Map<String, String> body = new HashMap<>();
            body.put("shipDate", LocalDate.now().toString());

            return HttpClientUtils.executePostRequest(String.format(properties.getShipTransferShipmentUrl(), shipmentId),
                SerializationUtils.serialize(body),
                headers,
                FinaleTransferShipmentResponse.class);
        } catch (Exception e) {
            log.error("[shipShipment] Error creating transfer shipment", e);
        }
        return null;
    }

    public FinaleTransferShipmentResponse receiveTransferShipment(String shipmentId) {
        try {
            Map<String, String> headers = buildHeaders();
            Map<String, String> body = new HashMap<>();
            body.put("receiveDate", LocalDate.now().toString());

            return HttpClientUtils.executePostRequest(String.format(properties.getReceiveTransferShipmentUrl(), shipmentId),
                SerializationUtils.serialize(body),
                headers,
                FinaleTransferShipmentResponse.class);
        } catch (Exception e) {
            log.error("[receiveShipment] Error creating transfer shipment", e);
        }
        return null;
    }

    public PurchaseOrderResponse createPurchaseOrder(CreatePurchaseOrderDto createPurchaseOrderDto) {
        try {
            Map<String, String> headers = buildHeaders();

            return HttpClientUtils.executePostRequest(properties.getCreatePurchaseOrderUrl(),
                SerializationUtils.serialize(createPurchaseOrderDto),
                headers,
                PurchaseOrderResponse.class);
        } catch (Exception e) {
            log.error("[createPurchaseOrder] Error creating purchase order", e);
        }
        return null;
    }

    public FinaleTransferShipmentResponse createShipment(CreateShipmentDto createShipmentDto) {
        try {
            Map<String, String> headers = buildHeaders();

            return HttpClientUtils.executePostRequest(properties.getCreateShipmentUrl(),
                SerializationUtils.serialize(createShipmentDto),
                headers,
                FinaleTransferShipmentResponse.class);
        } catch (Exception e) {
            log.error("[createShipment] Error creating shipment", e);
        }
        return null;
    }

    public ReceivePurchaseOrderResponse receivePurchaseOrder(ReceivePurchaseOrderDto receivePurchaseOrderDto) {
        try {
            Map<String, String> headers = buildHeaders();

            return HttpClientUtils.executePostRequest(properties.getReceivePurchaseOrderUrl(),
                SerializationUtils.serialize(receivePurchaseOrderDto),
                headers,
                ReceivePurchaseOrderResponse.class);
        } catch (Exception e) {
            log.error("[receivePurchaseOrder] Error creating purchase order", e);
        }
        return null;
    }

    public ShipmentResponse receiveShipment(String shipmentId) {
        try {
            Map<String, String> headers = buildHeaders();
            Map<String, String> body = new HashMap<>();
            body.put("receiveDate", LocalDate.now().toString());

            return HttpClientUtils.executePostRequest(String.format(properties.getReceiveShipmentUrl(), shipmentId),
                SerializationUtils.serialize(body),
                headers,
                ShipmentResponse.class);
        } catch (Exception e) {
            log.error("[receiveShipment] Error receive shipment", e);
        }
        return null;
    }

    public PurchaseOrderResponse completePurchaseOrder(String orderId) {
        try {
            Map<String, String> headers = buildHeaders();
            return HttpClientUtils.executePostRequest(String.format(properties.getCompletePurchaseOrderUrl(), orderId),
                SerializationUtils.serialize(new HashMap<>()),
                headers,
                PurchaseOrderResponse.class);
        } catch (Exception e) {
            log.error("[completePurchaseOrder] Error complete shipment", e);
        }
        return null;
    }

}
