package com.mercaso.wms.delivery.domain.customer;

import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyCustomerDto;
import com.mercaso.wms.domain.BaseDomain;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class Customer extends BaseDomain {

    private final UUID id;
    private String firstName;
    private String lastName;
    private String phone;
    private String email;
    private String externalId;

    public Customer create(ShopifyCustomerDto customerDto) {
        this.externalId = customerDto.getId();
        this.email = customerDto.getEmail();
        this.firstName = customerDto.getFirstName();
        this.lastName = customerDto.getLastName();
        this.phone = customerDto.getPhone();
        return this;
    }

}
