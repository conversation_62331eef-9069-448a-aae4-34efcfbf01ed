package com.mercaso.wms.interfaces.util;

import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject.PickingTaskDo;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject.ReceivingTaskDo;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.dataobject.TransferTaskDo;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.util.CollectionUtils;

public class SortByParseUtil {

    private SortByParseUtil() {
    }

    public static Sort getSortField(String sortField) {
        if (StringUtils.isEmpty(sortField)) {
            return null;
        }

        int lastUnderscore = sortField.lastIndexOf("_");
        if (lastUnderscore == -1) {
            return null;
        }

        String field = sortField.substring(0, lastUnderscore);
        String order = sortField.substring(lastUnderscore + 1);

        if (StringUtils.isEmpty(field) || StringUtils.isEmpty(order)) {
            return null;
        }

        field = toCamelCase(field, false);
        if ("ASC".equalsIgnoreCase(order)) {
            return Sort.by(Sort.Order.asc(field));
        } else if ("DESC".equalsIgnoreCase(order)) {
            return Sort.by(Sort.Order.desc(field));
        }

        return null;
    }

    public static Sort getCamelCaseSortFields(List<SortType> sortFields, EntityEnums entity) {
        if (CollectionUtils.isEmpty(sortFields)) {
            return null;
        }
        List<Sort.Order> orders = new ArrayList<>();
        for (SortType sortField : sortFields) {
            String field = getField(sortField.toString(), entity);
            String sortDirection = getSortDirection(sortField.toString());
            if (StringUtils.isEmpty(field) || StringUtils.isEmpty(sortDirection)) {
                continue;
            }
            orders.add(new Sort.Order(Sort.Direction.fromString(sortDirection), field));
        }
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        return Sort.by(orders);
    }

    public static Sort getOriginalSortFields(List<SortType> sortFields) {
        if (CollectionUtils.isEmpty(sortFields)) {
            return null;
        }
        List<Sort.Order> orders = new ArrayList<>();
        for (SortType sortField : sortFields) {
            String field = getOriginalField(sortField.toString());
            String sortDirection = getSortDirection(sortField.toString());
            if (StringUtils.isEmpty(field) || StringUtils.isEmpty(sortDirection)) {
                continue;
            }
            orders.add(new Sort.Order(Sort.Direction.fromString(sortDirection), field));
        }
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        return Sort.by(orders);
    }

    private static String getField(String sortField, EntityEnums entity) {
        if (StringUtils.isEmpty(sortField)) {
            return null;
        }

        int lastUnderscore = sortField.lastIndexOf("_");
        if (lastUnderscore == -1) {
            return null;
        }

        String field = toCamelCase(sortField.substring(0, lastUnderscore), entity != EntityEnums.PICKING_TASK);

        return switch (entity) {
            case ACCOUNT -> "da." + field;
            case ACCOUNT_PREFERENCE -> "ap." + field;
            case DELIVERY_TASK -> {
                if (Objects.equals(sortField, SortType.ACTIVE_ORDER_DELAY_MINUTES_ASC.name()) || Objects.equals(sortField,
                    SortType.ACTIVE_ORDER_DELAY_MINUTES_DESC.name())) {
                    yield "active_order_delay_minutes";
                } else {
                    yield isFieldInClass(field, DeliveryTaskDo.class) ? "dt." + field : "dorder." + field;

                }
            }
            case DELIVERY_ORDER -> isFieldInClass(field, DeliveryOrderDo.class) ? "ddo." + field : "doi." + field;
            case PICKING_TASK -> isNativeFieldInClass(field, PickingTaskDo.class) ? "pt." + field : "pti." + field;
            case RECEIVING_TASK -> isFieldInClass(field, ReceivingTaskDo.class) ? "rt." + field : "rti." + field;
            case TRANSFER_TASK -> isFieldInClass(field, TransferTaskDo.class) ? "tt." + field : "tti." + field;
            default -> null;
        };
    }

    private static String getOriginalField(String sortField) {
        if (StringUtils.isEmpty(sortField)) {
            return null;
        }

        int lastUnderscore = sortField.lastIndexOf("_");
        if (lastUnderscore == -1) {
            return null;
        }
        String field = sortField.substring(0, lastUnderscore);
        return toCamelCase(field, false);
    }

    private static boolean isFieldInClass(String fieldName, Class<?> clazz) {
        while (clazz != null) {
            try {
                clazz.getDeclaredField(toCamelCase(fieldName, false));
                return true;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return false;
    }

    private static boolean isNativeFieldInClass(String fieldName, Class<?> clazz) {
        while (clazz != null) {
            try {
                clazz.getDeclaredField(fieldName);
                return true;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return false;
    }

    public static String getSortDirection(String sortField) {
        if (StringUtils.isEmpty(sortField)) {
            return null;
        }

        int lastUnderscore = sortField.lastIndexOf("_");
        if (lastUnderscore == -1) {
            return null;
        }

        String order = sortField.substring(lastUnderscore + 1);
        if ("ASC".equalsIgnoreCase(order)) {
            return "ASC";
        } else {
            return "DESC";
        }
    }

    private static String toCamelCase(String input, boolean nativeSql) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        String[] parts = input.toLowerCase().split("_");
        if (parts.length == 1) {
            return input.toLowerCase();
        }
        StringBuilder camelCaseString = new StringBuilder(parts[0]);

        for (int i = 1; i < parts.length; i++) {
            if (nativeSql) {
                camelCaseString.append("_").append(StringUtils.toRootLowerCase(parts[i]));
            } else {
                camelCaseString.append(StringUtils.capitalize(parts[i]));
            }
        }

        return camelCaseString.toString();
    }

}
