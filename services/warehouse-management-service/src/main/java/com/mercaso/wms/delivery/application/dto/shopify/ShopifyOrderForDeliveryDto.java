package com.mercaso.wms.delivery.application.dto.shopify;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopifyOrderForDeliveryDto {

    private String id;
    private String name;
    @JsonProperty("fulfillment_status")
    private String fulfillmentStatus;
    @JsonProperty("updated_at")
    private Instant updatedAt;
    @JsonProperty("cancelled_at")
    private Instant cancelledAt;
    private String tags;
    @JsonProperty("shipping_address")
    private ShippingAddressDto shippingAddress;
    @JsonProperty("line_items")
    private List<ShopifyLineItemDto> lineItems;
    private String note;
    @JsonProperty("financial_status")
    private String financialStatus;
    @JsonProperty("customer")
    private ShopifyCustomerDto customer;
    @JsonProperty("total_price")
    private BigDecimal totalPrice;
    @JsonProperty("current_total_discounts")
    private BigDecimal currentTotalDiscounts;
    @JsonProperty("discount_applications")
    private List<DiscountApplicationDto> discountApplications;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopifyCustomerDto {

        private String id;
        private String email;
        @JsonProperty("first_name")
        private String firstName;
        @JsonProperty("last_name")
        private String lastName;
        private String phone;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopifyLineItemDto {

        private String id;
        private String sku;
        @JsonProperty("current_quantity")
        private int currentQuantity;
        private int quantity;
        private String title;
        private BigDecimal price;
        @JsonProperty("total_discount")
        private BigDecimal totalDiscount;
        @JsonProperty("discount_allocations")
        private List<DiscountAllocationDto> discountAllocations;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShippingAddressDto {

        @JsonProperty("first_name")
        private String firstName;
        @JsonProperty("last_name")
        private String lastName;
        private String address1;
        private String address2;
        private String phone;
        private String city;
        private String zip;
        private String province;
        private String country;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private String name;


    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscountApplicationDto {

        private String code;
        private String type;
        @JsonProperty("target_type")
        private String targetType;
        @JsonProperty("value_type")
        private String valueType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscountAllocationDto {

        @JsonProperty("amount")
        private BigDecimal amount;
        @JsonProperty("discount_application_index")
        private int discountApplicationIndex;
    }
}
