package com.mercaso.wms.delivery.infrastructure.interceptor;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import com.mercaso.wms.delivery.infrastructure.exception.SingleDeviceLoginException;
import com.mercaso.wms.delivery.infrastructure.service.LoginVersionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor that ensures a user can only be logged in on one device at a time.
 * Compares the login timestamp in the token with the one stored in the database.
 * Uses the LoginVersionService to handle the validation logic.
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class SingleDeviceLoginCheckInterceptor implements HandlerInterceptor {

    private static final String BEARER_PREFIX = "Bearer ";
    private static final String DRIVER_ROLE = "user_group_driver";
    private static final List<String> SKIP_PROFILE = List.of("integration", "local");
    private static final String CONNECTION_MOBILE_CONNECTION = "Mobile-connection";

    private final LoginVersionService loginVersionService;
    private final Environment environment;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
        @NotNull Object handler) {
        if (isIntegrationEnvironment()) {
            log.debug("Integration environment detected, skipping single device login check");
            return true;
        }

        if (!(handler instanceof HandlerMethod method) || !shouldCheckLoginVersion(method)) {
            return true;
        }

        if (!validateAuthorizationHeader(request, response)) {
            return false;
        }

        String userId = SecurityContextUtil.getLoginUserId();
        String tokenLoginVersion = SecurityContextUtil.getLastLoginTime();
        List<String> mercasoRoles = SecurityContextUtil.getMercasoRoles();
        String connection = SecurityContextUtil.getConnectionStrategy();


        if (StringUtils.isBlank(tokenLoginVersion)) {
            throw new SingleDeviceLoginException(userId, "Last login time is missing in the token.");
        }



        try {
            if (CollectionUtils.isNotEmpty(mercasoRoles) && mercasoRoles.contains(DRIVER_ROLE) && CONNECTION_MOBILE_CONNECTION.equals(connection)) {

                loginVersionService.validateLoginVersion(userId, tokenLoginVersion);
            }else {

                log.info("User {} is not a driver or the connection is not mobile-connection, skip the single device login check", userId);
                return true;
            }

        } catch (SingleDeviceLoginException e) {
            handleLoginException(e, response);
            return false;
        } catch (Exception e) {
            log.warn("Login Version [{}], unexpected error during login version check", tokenLoginVersion, e);
            return true;
        }
        return true;
    }

    private boolean isIntegrationEnvironment() {
        for (String profile : environment.getActiveProfiles()) {
            if (SKIP_PROFILE.contains(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Determines if login version check should be performed based on annotations
     */
    private boolean shouldCheckLoginVersion(HandlerMethod method) {
        boolean checkLoginVersion = method.hasMethodAnnotation(SingleDeviceLoginCheck.class)
            || method.getBeanType().isAnnotationPresent(SingleDeviceLoginCheck.class);

        if (!checkLoginVersion) {
            log.debug("Not a login version request, skipping login version check.");
        }

        return checkLoginVersion;
    }

    /**
     * Validates that the Authorization header exists and contains a Bearer token
     */
    private boolean validateAuthorizationHeader(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (token == null || !token.startsWith(BEARER_PREFIX)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            log.error("Authorization header is missing or invalid.");
            return false;
        }
        return true;
    }

    /**
     * Handles login exceptions by setting appropriate response status and logging
     */
    private void handleLoginException(SingleDeviceLoginException e, HttpServletResponse response) {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());

        if (e.getTokenVersion() != null && e.getDbVersion() != null) {
            log.warn("Login version mismatch: token version = {}, db version = {}, user = {}",
                e.getTokenVersion(), e.getDbVersion(), e.getUserId());
        } else {
            log.error("Login validation failed: {}", e.getMessage());
        }
    }
}
