package com.mercaso.wms.delivery.infrastructure.repository.customer.jpa;

import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.dataobject.CustomerDo;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CustomerJpaDao extends JpaRepository<CustomerDo, UUID> {

    CustomerDo findByEmail(String email);

    CustomerDo findByExternalId(String externalId);

}